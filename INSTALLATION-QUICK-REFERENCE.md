# Ledvance CLI 安装快速参考

## 🚀 快速安装

### 生产环境（推荐）

```bash
# 方法1: 自动安装脚本
python scripts/install-production.py

# 方法2: 平台特定脚本
scripts/install-production.bat    # Windows
scripts/install-production.sh     # Linux/macOS

# 方法3: 手动安装
pip install .
```

### 开发环境

```bash
# 开发模式安装
pip install -e .

# 验证安装
python scripts/verify-installation.py
```

## 📦 构建和分发

```bash
# 构建分发包
python scripts/build-package.py

# 安装构建的包
pip install dist/ledvance_cli-*.whl
```

## 🔧 自动补全

```bash
# 自动安装补全
python scripts/install-completion.py

# 手动安装补全
ledvance completion bash --install      # Bash
ledvance completion zsh --install       # Zsh
ledvance completion fish --install      # Fish
ledvance completion powershell --install # PowerShell
```

## ✅ 验证安装

```bash
# 完整验证
python scripts/verify-installation.py

# 快速检查
ledvance --version
ledvance --help
```

## 🎯 使用示例

```bash
# 查看所有命令
ledvance --help

# 涂鸦平台登录
ledvance tuya-login

# 检查面板版本
ledvance ezviz-panel-check --env prod

# 同步产品
ledvance tuya-sync-product --env test --pid TY001
```

## 🔍 故障排除

```bash
# 命令未找到
pip show ledvance-cli
echo $PATH

# 权限问题
pip install --user .

# 依赖冲突
python -m venv venv
source venv/bin/activate  # Linux/macOS
# 或 venv\Scripts\activate  # Windows
pip install .
```

## 📚 更多信息

- [详细安装指南](docs/installation.md)
- [生产环境部署](docs/production-deployment.md)
- [Shell自动补全](docs/shell-completion.md)
- [项目README](README.md)
