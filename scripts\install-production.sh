#!/bin/bash
# Linux/macOS脚本 - Ledvance CLI生产环境安装

echo "🚀 Ledvance CLI 生产环境安装程序"
echo "========================================"
echo

# 检查Python是否可用
if ! command -v python3 &> /dev/null && ! command -v python &> /dev/null; then
    echo "❌ 错误: 未找到Python"
    echo "请先安装Python 3.8或更高版本"
    echo "Ubuntu/Debian: sudo apt install python3 python3-pip"
    echo "CentOS/RHEL: sudo yum install python3 python3-pip"
    echo "macOS: brew install python3"
    exit 1
fi

# 确定Python命令
if command -v python3 &> /dev/null; then
    PYTHON_CMD="python3"
    PIP_CMD="python3 -m pip"
elif command -v python &> /dev/null; then
    PYTHON_CMD="python"
    PIP_CMD="python -m pip"
fi

echo "✅ Python环境检查通过"
$PYTHON_CMD --version

# 检查Python版本
python_version=$($PYTHON_CMD -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')")
required_version="3.8"

if [ "$(printf '%s\n' "$required_version" "$python_version" | sort -V | head -n1)" != "$required_version" ]; then
    echo "❌ 错误: Python版本过低 (当前: $python_version, 需要: $required_version+)"
    exit 1
fi

# 检查pip是否可用
if ! $PIP_CMD --version &> /dev/null; then
    echo "❌ 错误: pip不可用"
    echo "请安装pip: sudo apt install python3-pip"
    exit 1
fi

echo "✅ pip工具检查通过"

# 询问安装模式
echo
echo "📦 选择安装模式:"
echo "1. 生产模式 (推荐) - 正式使用"
echo "2. 开发模式 - 开发调试"
echo

while true; do
    read -p "请选择 (1/2): " choice
    case $choice in
        1)
            install_mode="production"
            echo "选择的安装模式: 生产模式"
            break
            ;;
        2)
            install_mode="development"
            echo "选择的安装模式: 开发模式"
            break
            ;;
        *)
            echo "请输入 1 或 2"
            ;;
    esac
done

echo
echo "📦 开始安装Ledvance CLI..."

# 执行安装
if [ "$install_mode" = "production" ]; then
    echo "正在安装 (生产模式)..."
    if ! $PIP_CMD install .; then
        echo "❌ 安装失败"
        exit 1
    fi
else
    echo "正在安装 (开发模式)..."
    if ! $PIP_CMD install -e .; then
        echo "❌ 安装失败"
        exit 1
    fi
fi

echo "✅ Ledvance CLI安装成功"

# 验证安装
echo
echo "🔍 验证安装..."
if ! ledvance --version; then
    echo "❌ 安装验证失败"
    exit 1
fi

echo "✅ 安装验证通过"

# 询问是否安装自动补全
echo
read -p "是否安装shell自动补全? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "📦 安装自动补全..."
    if $PYTHON_CMD scripts/install-completion.py; then
        echo "✅ 自动补全安装成功"
    else
        echo "⚠️  自动补全安装失败，可稍后手动配置"
        echo "运行: python scripts/install-completion.py"
    fi
else
    echo "⏭️  跳过自动补全安装"
    echo "稍后可运行: ledvance completion <shell> --install"
fi

# 询问是否创建配置文件
echo
if [ ! -f "config.yaml" ]; then
    read -p "是否创建配置文件模板? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        if [ -f "config.yaml.example" ]; then
            cp "config.yaml.example" "config.yaml"
            echo "✅ 配置文件已创建: config.yaml"
            echo "💡 请编辑配置文件以适应您的环境"
        else
            echo "⚠️  未找到配置文件模板"
        fi
    else
        echo "⏭️  跳过配置文件创建"
    fi
else
    echo "✅ 发现现有配置文件: config.yaml"
fi

# 显示使用指南
echo
echo "========================================"
echo "🎉 安装完成！"
echo "========================================"
echo
echo "📋 基本命令:"
echo "  ledvance --help                    # 查看帮助"
echo "  ledvance --version                 # 查看版本"
echo "  ledvance tuya-login                # 涂鸦平台登录"
echo "  ledvance ezviz-panel-check --help  # 查看面板检查帮助"
echo
echo "🔧 配置:"
echo "  编辑 config.yaml 文件配置环境参数"
echo "  设置环境变量覆盖配置"
echo
echo "📚 更多信息:"
echo "  docs/installation.md     # 详细安装指南"
echo "  docs/shell-completion.md # 自动补全指南"
echo "  README.md                # 项目说明"
echo

if [ "$install_mode" = "development" ]; then
    echo "💡 开发模式提示:"
    echo "  源码修改会立即生效"
    echo "  请不要删除源码目录"
    echo
fi

echo "🚀 开始使用Ledvance CLI吧！"
