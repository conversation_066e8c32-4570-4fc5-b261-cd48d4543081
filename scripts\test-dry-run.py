#!/usr/bin/env python3
"""
测试所有CLI命令的--dry-run功能
"""
import subprocess
import sys
import logging
from pathlib import Path

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def run_command(cmd_args, description):
    """运行命令并检查结果"""
    logger.info(f"🧪 测试: {description}")
    logger.info(f"📝 命令: {' '.join(cmd_args)}")
    
    try:
        result = subprocess.run(
            cmd_args,
            capture_output=True,
            text=True,
            timeout=30
        )
        
        if result.returncode == 0:
            logger.info("✅ 命令执行成功")
            # 检查输出中是否包含dry-run标识
            if "[DRY-RUN]" in result.stdout or "[DRY-RUN]" in result.stderr:
                logger.info("✅ 检测到dry-run模式输出")
            else:
                logger.warning("⚠️  未检测到dry-run模式标识")
            return True
        else:
            logger.error(f"❌ 命令执行失败 (退出码: {result.returncode})")
            logger.error(f"错误输出: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        logger.error("❌ 命令执行超时")
        return False
    except Exception as e:
        logger.error(f"❌ 命令执行异常: {e}")
        return False

def test_all_dry_run_commands():
    """测试所有命令的dry-run功能"""
    logger.info("🚀 开始测试所有CLI命令的--dry-run功能")
    logger.info("=" * 60)
    
    # 定义测试用例
    test_cases = [
        {
            "cmd": ["ledvance", "--dry-run", "tuya-login", "--output-file", "test-cookies.json"],
            "description": "涂鸦平台登录"
        },
        {
            "cmd": ["ledvance", "--dry-run", "tuya-dp-check", "--dp", "switch_led", "--category", "LightSource"],
            "description": "涂鸦DP检查"
        },
        {
            "cmd": ["ledvance", "--dry-run", "tuya-sync-dp", "--env", "Test", "--category", "test_category", "--pid", "test_pid"],
            "description": "涂鸦DP同步"
        },
        {
            "cmd": ["ledvance", "--dry-run", "tuya-sync-product", "--env", "Test", "--main-category", "test_main", "--category", "test_cat", "--rn-name", "test_panel", "--pids", "TY001,TY002"],
            "description": "涂鸦产品同步"
        },
        {
            "cmd": ["ledvance", "--dry-run", "ezviz-panel-check", "--env", "Test", "--type", "Gray"],
            "description": "Ezviz面板检查"
        },
        {
            "cmd": ["ledvance", "--dry-run", "ezviz-panel-upgrade", "--env", "Test", "--type", "Upgrade", "--modules", "test_module"],
            "description": "Ezviz面板升级"
        }
    ]
    
    # 执行测试
    success_count = 0
    total_count = len(test_cases)
    
    for i, test_case in enumerate(test_cases, 1):
        logger.info(f"\n📋 测试 {i}/{total_count}: {test_case['description']}")
        logger.info("-" * 40)
        
        if run_command(test_case["cmd"], test_case["description"]):
            success_count += 1
        
        logger.info("")
    
    # 输出测试结果
    logger.info("=" * 60)
    logger.info("📊 测试结果汇总")
    logger.info(f"✅ 成功: {success_count}/{total_count}")
    logger.info(f"❌ 失败: {total_count - success_count}/{total_count}")
    
    if success_count == total_count:
        logger.info("🎉 所有dry-run测试通过！")
        return True
    else:
        logger.error("💥 部分dry-run测试失败！")
        return False

def test_help_messages():
    """测试帮助信息中是否包含--dry-run选项"""
    logger.info("\n🔍 检查帮助信息中的--dry-run选项")
    logger.info("-" * 40)
    
    try:
        result = subprocess.run(
            ["ledvance", "--help"],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if "--dry-run" in result.stdout:
            logger.info("✅ 全局--dry-run选项存在于帮助信息中")
            return True
        else:
            logger.error("❌ 全局--dry-run选项未在帮助信息中找到")
            return False
            
    except Exception as e:
        logger.error(f"❌ 检查帮助信息失败: {e}")
        return False

def main():
    """主函数"""
    logger.info("🧪 Ledvance CLI 改进版 Dry-Run 功能测试")
    logger.info("=" * 60)
    logger.info("测试改进后的dry-run模式：")
    logger.info("✅ 执行完整的命令流程")
    logger.info("✅ 在关键操作点提供mock数据")
    logger.info("✅ 保持真实的执行路径")
    logger.info("✅ 详细的操作日志")
    logger.info("✅ 模拟真实的返回值")
    logger.info("=" * 60)

    # 检查ledvance命令是否可用
    try:
        result = subprocess.run(["ledvance", "--version"], capture_output=True, text=True, timeout=10)
        if result.returncode != 0:
            logger.error("❌ ledvance命令不可用，请确保已正确安装")
            sys.exit(1)
        logger.info(f"✅ ledvance命令可用: {result.stdout.strip()}")
    except Exception as e:
        logger.error(f"❌ 无法执行ledvance命令: {e}")
        sys.exit(1)

    # 执行测试
    help_test_passed = test_help_messages()
    dry_run_tests_passed = test_all_dry_run_commands()

    # 最终结果
    if help_test_passed and dry_run_tests_passed:
        logger.info("\n🎉 所有测试通过！改进版dry-run功能工作正常。")
        logger.info("🔍 改进后的dry-run模式特点：")
        logger.info("  ✅ 完整执行命令流程，不在开头直接返回")
        logger.info("  ✅ 在API调用点使用模拟数据")
        logger.info("  ✅ 在文件操作点显示模拟操作日志")
        logger.info("  ✅ 保持真实的业务逻辑执行路径")
        logger.info("  ✅ 提供详细的[DRY-RUN]标识日志")
        sys.exit(0)
    else:
        logger.error("\n💥 测试失败！请检查dry-run功能实现。")
        sys.exit(1)

if __name__ == "__main__":
    main()
