# Ledvance CLI 简化版 Dry-Run 实现指南

## 概述

本指南说明了Ledvance CLI工具简化版dry-run实现模式。这种实现方式在保持代码简洁的同时，只在关键操作点进行mock，提供了平衡的用户体验。

## 设计原则

### 1. 简洁性优先
- 只在真正关键的位置添加dry-run检查
- 避免过度详细的日志输出
- 保持代码的可读性和维护性

### 2. 关键操作点Mock
- **API调用**：替换为模拟数据
- **文件操作**：显示模拟操作信息
- **网络请求**：跳过实际请求
- **浏览器操作**：模拟整个流程

### 3. 统一的日志格式
- 使用`🔍 [DRY-RUN]`前缀标识模拟操作
- 保持日志信息简洁明了
- 避免冗余的步骤描述

## 实现模式

### 基本结构
```python
def command_function(ctx: click.Context, param: str):
    is_dry_run = ctx.obj.dry_run
    
    logger.info("开始执行命令...")
    if is_dry_run:
        logger.info("🔍 [DRY-RUN] 运行在模拟模式")
    
    # 正常的业务逻辑...
    
    # 关键操作点1: API调用
    if is_dry_run:
        logger.info("🔍 [DRY-RUN] 模拟API调用: api_name")
        result = create_mock_data()
    else:
        result = actual_api_call()
    
    # 继续处理...
    
    # 关键操作点2: 文件操作
    if is_dry_run:
        logger.info(f"🔍 [DRY-RUN] 模拟保存文件: {file_path}")
    else:
        save_file(data, file_path)
```

## 已实现的命令示例

### 1. ezviz_panel_check.py
```python
# 配置缺失时的处理
if not product_list:
    if is_dry_run:
        logger.info("🔍 [DRY-RUN] 使用模拟产品列表")
        product_list = [mock_products...]
    else:
        raise CLIError("未配置产品列表")

# API调用mock
if is_dry_run:
    logger.info("🔍 [DRY-RUN] 模拟API调用: get_product_panel_version")
    version_list = [mock_version_data...]
else:
    version_list = ezviz_api.get_product_panel_version(...)

# 文件操作mock
if is_dry_run:
    logger.info(f"🔍 [DRY-RUN] 模拟保存文件: {output_file}")
else:
    save_to_file(data, output_file)
```

### 2. tuya_login.py
```python
# 配置缺失时的处理
if not tuya_username or not tuya_password:
    if is_dry_run:
        logger.info("🔍 [DRY-RUN] 使用模拟登录凭据")
        tuya_username = "<EMAIL>"
        tuya_password = "mock_password_123"
    else:
        raise CLIError("未配置用户名或密码")

# 浏览器操作mock
if is_dry_run:
    logger.info("🔍 [DRY-RUN] 模拟浏览器登录流程")
    cookie_dict = create_mock_cookies()
else:
    # 实际的浏览器操作...
    cookie_dict = perform_browser_login()

# 文件保存mock
if is_dry_run:
    logger.info(f"🔍 [DRY-RUN] 模拟保存cookies到: {cookies_path}")
else:
    save_cookies(cookie_dict, cookies_path)
```

### 3. tuya_dp_check.py
```python
# API调用mock
if is_dry_run:
    logger.info("🔍 [DRY-RUN] 模拟API调用: _fetch_all_products")
    products = [mock_products...]
else:
    products = _fetch_all_products(base_payload)

# 在循环中的API调用mock
for product in products:
    if is_dry_run:
        # 简单的随机模拟，不需要详细日志
        dps = create_random_mock_dps(dp_code)
    else:
        dps = tuya_api.get_product_dp(product['id'])
```

## Mock数据设计

### 1. 保持数据结构一致性
```python
# 真实数据结构
{
    "productId": "TY_REAL_001",
    "rnName": "real-panel-name",
    "version": "V1.2.3"
}

# Mock数据结构（保持一致）
{
    "productId": "TY_LDV_mock_001", 
    "rnName": "rn-panel-lightsource",
    "version": "V1.5.0"
}
```

### 2. 适度的随机性
```python
# 使用简单的随机逻辑
import random
has_target_dp = random.choice([True, False])

# 或使用hash生成确定性的"随机"数据
mock_version = f"V1.{hash(item['productId']) % 10}.0"
```

### 3. 简洁的Mock数据创建
```python
def create_mock_cookies():
    return [
        {"name": "sessionId", "value": "mock_session_12345"},
        {"name": "csrf-token", "value": "mock_csrf_token_abcdef"},
        {"name": "loginTime", "value": str(int(time.time()))}
    ]
```

## 错误处理

### 简化的异常处理
```python
except CLIError as e:
    logger.error(f"操作失败: {e}")
    if not is_dry_run:  # 只在非dry-run模式下退出
        ctx.exit(1)
except Exception as e:
    logger.error(f"发生意外错误: {e}", exc_info=True)
    if not is_dry_run:
        ctx.exit(1)
```

## 开发指南

### 为新命令添加简化版dry-run支持

1. **添加基础检查**
```python
@click.pass_context
def new_command(ctx: click.Context, param: str):
    is_dry_run = ctx.obj.dry_run
    
    logger.info("开始执行命令...")
    if is_dry_run:
        logger.info("🔍 [DRY-RUN] 运行在模拟模式")
```

2. **识别关键操作点**
- API调用
- 文件读写操作
- 网络请求
- 外部程序调用

3. **添加简洁的Mock**
```python
# 只在关键点添加mock，保持简洁
if is_dry_run:
    logger.info("🔍 [DRY-RUN] 模拟操作: 简短描述")
    result = mock_data
else:
    result = actual_operation()
```

4. **避免过度Mock**
- 不要为每个小步骤都添加dry-run日志
- 不要在业务逻辑处理中添加dry-run检查
- 专注于有副作用的操作

## 最佳实践

### ✅ 推荐做法
```python
# 简洁的API调用mock
if is_dry_run:
    logger.info("🔍 [DRY-RUN] 模拟API调用: get_products")
    products = create_mock_products()
else:
    products = api.get_products()

# 简洁的文件操作mock
if is_dry_run:
    logger.info(f"🔍 [DRY-RUN] 模拟保存文件: {filename}")
else:
    save_file(data, filename)
```

### ❌ 避免做法
```python
# 过度详细的日志
if is_dry_run:
    logger.info("🔍 [DRY-RUN] 开始构建API请求参数...")
    logger.info("🔍 [DRY-RUN] 设置请求头...")
    logger.info("🔍 [DRY-RUN] 构建请求体...")
    logger.info("🔍 [DRY-RUN] 发送HTTP请求...")
    # 太繁琐了！

# 在业务逻辑中添加不必要的检查
for item in items:
    if is_dry_run:
        logger.info(f"🔍 [DRY-RUN] 处理项目: {item}")
    process_item(item)  # 这里不需要dry-run检查
```

## 测试验证

### 自动化测试
```bash
python scripts/test-dry-run.py
```

### 手动测试
```bash
# 测试关键功能
ledvance --dry-run ezviz-panel-check --env Test --type Gray
ledvance --dry-run tuya-login --output-file test.json
ledvance --dry-run tuya-dp-check --dp switch_led --category LightSource
```

## 总结

简化版dry-run实现的特点：

✅ **代码简洁** - 只在关键位置添加mock
✅ **易于维护** - 减少了冗余的日志和检查
✅ **用户友好** - 提供清晰但不冗长的操作信息
✅ **功能完整** - 覆盖所有有副作用的操作
✅ **性能良好** - 最小化的运行时开销

这种实现方式在保持dry-run功能完整性的同时，避免了过度复杂的实现，是实用性和简洁性的良好平衡。
