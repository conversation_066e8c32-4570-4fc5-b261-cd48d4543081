# Configuration file for the application
# Settings here can be overridden by environment variables

# Retry mechanism settings
retry:
  attempts: 3
  delay: 5 # in seconds

# Base directory for file outputs
base_dir: "D:/OneDrive - LEDVANCE GmbH/rn-package"
temp_img_dir=: "D:/temp/tyIcon"

# Panel check configuration
min_app_version: "2.0.16"
panel_check_products:
  - productId: "TY_LDV_xxxxxxxxxx"
    version: ${min_app_version}
    category: "LightSource"
  - productId: "TY_LDV_yyyyyyyyyy"
    version: ${min_app_version}
    category: "MatterLight"

default_device_modules:
  - cloud-rn-module-light-source
  - cloud-rn-module-matter-light

default_global_modules:
  - cloud-rn-group-module-light-source
  - cloud-rn-group-module-matter-light

device_module_map:
  cloud-rn-module-light-source: cloud-rn-module-light-source
  cloud-rn-module-matter-light: cloud-rn-module-matter-light

gray_accounts:
  - <EMAIL>

product_categories:
  - LightSource
  - MatterLight
