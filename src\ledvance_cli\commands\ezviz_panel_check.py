"""
检查 Ezviz 面板版本的命令。
"""
import json
import logging
import os
from pathlib import Path

import click

from ledvance_cli import config as app_config
from ledvance_cli.core import ezviz_api
from ledvance_cli.core.exceptions import CLIError
from ledvance_cli.utils import format_output

logger = logging.getLogger(__name__)


@click.command("ezviz-panel-check")
@click.option(
    "--env", 
    default="Prod", 
    type=click.Choice(['Prod', 'Test'], case_sensitive=True), 
    help="要操作的平台环境：'Prod' (生产) 或 'Test' (测试)。"
)
@click.option(
    "--type",
    "check_type",
    default="Gray",
    type=click.Choice(['Gray', 'Release'], case_sensitive=True),
    help="检查类型：'Gray' (灰度) 或 'Release' (发布)。"
)
@click.option(
    "--app-version",
    help="最低支持的 app 版本。"
)
@click.option(
    "--output-dir",
    type=click.Path(path_type=Path),
    help="输出文件的目录。如果未指定，使用配置中的 base_dir。"
)
@click.pass_context
def ezviz_panel_check(
    ctx: click.Context,
    env: str,
    check_type: str,
    app_version: str,
    output_dir: Path,
):
    """
    检查 Ezviz 面板版本信息。

    此命令会自动处理 App 登录和会话管理，获取指定产品的面板版本信息，
    并将结果保存到 JSON 文件中。
    """
    output_format = ctx.obj.output_format
    is_dry_run = ctx.obj.dry_run
    min_app_version = app_version or app_config.APP_CONFIG.get("min_app_version", "1.0.0")

    logger.info(f"开始检查面板版本 (环境: {env}, 类型: {check_type}, App版本: {min_app_version})")
    if is_dry_run:
        logger.info("🔍 [DRY-RUN] 运行在模拟模式")

    try:
        # 获取产品列表
        product_list = app_config.APP_CONFIG.get("panel_check_products", [])
        if not product_list:
            if is_dry_run:
                # 在dry-run模式下提供模拟的产品列表
                logger.info("🔍 [DRY-RUN] 使用模拟产品列表")
                product_list = [
                    {"productId": "TY_LDV_mock_product_001", "category": "LightSource"},
                    {"productId": "TY_LDV_mock_product_002", "category": "MatterLight"},
                    {"productId": "TY_LDV_mock_product_003", "category": "SmartSwitch"}
                ]
            else:
                raise CLIError("未配置产品列表，请在配置文件中设置 panel_check_products。")

        logger.info(f"使用产品列表，共 {len(product_list)} 个产品")

        # 为产品列表添加版本信息
        product_list_with_version = [
            {**item, 'version': min_app_version} for item in product_list
        ]

        # 获取请求体
        request_body = app_config.APP_CONFIG.get(f"ezviz_app_request_body_{check_type.lower()}")
        if not request_body:
            if is_dry_run:
                logger.info("🔍 [DRY-RUN] 使用模拟请求体")
                request_body = "mock_request_body_for_dry_run"
            else:
                raise CLIError(f"未配置 {check_type} 类型的请求体，请检查配置文件。")

        # 获取面板版本信息
        logger.info(f"正在获取 {len(product_list_with_version)} 个产品的面板版本信息...")

        if is_dry_run:
            logger.info("🔍 [DRY-RUN] 模拟API调用: get_product_panel_version")
            # 创建模拟数据
            version_list = []
            for item in product_list_with_version:
                mock_panel_name = f"rn-panel-{item['category'].lower()}"
                mock_version = f"V1.{hash(item['productId']) % 10}.0"
                version_list.append({
                    'productId': item['productId'],
                    'rnName': mock_panel_name,
                    'version': mock_version,
                    'appVersion': min_app_version
                })
        else:
            version_list = ezviz_api.get_product_panel_version(product_list_with_version, request_body)

        if not version_list:
            raise CLIError("获取面板版本信息失败。")

        # 处理结果
        res_list = []
        version_map = {}

        for item in version_list:
            # 找到对应的产品信息
            product = next(
                (p for p in product_list_with_version if p['productId'] == item['productId']),
                None
            )

            if product:
                panel_name = item.get('rnName', 'tuyaPanel')
                val = {
                    'category': product['category'],
                    'panel': panel_name,
                    'panel version': item['version'],
                    'min app version': item['appVersion']
                }
                res_list.append(val)
                version_map[panel_name] = item['version']

        if res_list:
            # 打印格式化的结果
            format_output(res_list, output_format, title=f"Ezviz 面板版本检查结果 ({env.upper()})")

            # 保存到文件
            if not output_dir:
                output_dir = Path(app_config.APP_CONFIG.get("base_dir", "."))

            output_file = output_dir / f"{env}-rn-panel-version.json"

            if is_dry_run:
                logger.info(f"🔍 [DRY-RUN] 模拟保存文件: {output_file}")
            else:
                output_dir.mkdir(parents=True, exist_ok=True)
                with open(output_file, "w", encoding="utf-8") as f:
                    json.dump(version_map, f, indent=4, ensure_ascii=False)

            logger.info(f"结果已保存到: {output_file}")

        else:
            logger.warning("未获取到任何面板版本信息。")

    except CLIError as e:
        logger.error(f"检查面板版本时出错: {e}")
        if not is_dry_run:
            ctx.exit(1)
    except Exception as e:
        logger.error(f"发生意外错误: {e}", exc_info=True)
        if not is_dry_run:
            ctx.exit(1)

    logger.info("面板版本检查完成。")
