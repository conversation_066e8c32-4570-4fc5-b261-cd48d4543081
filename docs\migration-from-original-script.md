# 从原始脚本迁移到 ezviz-panel-gray 命令

## 概述

本文档展示了如何从您提供的原始 Python 脚本迁移到新的 `ezviz-panel-gray` 命令。

## 原始脚本 vs 新命令对比

### 原始脚本调用方式

```python
# 原始脚本
python your_script.py --env Test --type Gray --name my-panel --pids PID001,PID002 --users <EMAIL>,<EMAIL>
```

### 新命令调用方式

```bash
# 新命令
ledvance ezviz-panel-gray --env Test --type Gray --name my-panel --pids PID001,PID002 --users <EMAIL>,<EMAIL>
```

## 功能对应表

| 原始脚本功能 | 新命令功能 | 说明 |
|-------------|-----------|------|
| `argparse` 参数解析 | Click 参数解析 | 更现代化的 CLI 框架 |
| `--env` 参数 | `--env` 参数 | 完全相同 |
| `--type` 参数 | `--type` 参数 | 完全相同 |
| `--name` 参数 | `--name` 参数 | 完全相同 |
| `--pids` 参数 | `--pids` 参数 | 完全相同 |
| `--users` 参数 | `--users` 参数 | 完全相同 |
| `TEST_ENV`, `GRAY_TYPE` 常量 | `app_config.TEST_ENV`, `app_config.GRAY_TYPE` | 集成到配置系统 |
| `GRAY_ACCOUNTS` 常量 | 配置文件 `gray_accounts` | 更灵活的配置方式 |
| `panel_gray()` 函数 | `_panel_gray_operation()` 函数 | 重构并增强 |
| 简单的 print 输出 | 结构化日志记录 | 更专业的日志系统 |
| 基本错误处理 | 完整错误处理 + 重试机制 | 更健壮的错误处理 |

## 代码结构对比

### 原始脚本结构

```python
import argparse
import sys
import requests
import ezvizApi
from config_secure import ProdConfig, TestConfig, TEST_ENV, GRAY_TYPE, GRAY_ACCOUNTS

def parse_arguments():
    # 参数解析逻辑
    pass

def panel_gray(session, pid=None):
    # 灰度操作逻辑
    pass

if __name__ == '__main__':
    # 主执行逻辑
    pass
```

### 新命令结构

```python
import logging
from typing import List, Optional
import click
from ledvance_cli import config as app_config
from ledvance_cli.core import ezviz_api
from ledvance_cli.core.exceptions import CLIError
from ledvance_cli.utils import format_output, show_progress

@click.command("ezviz-panel-gray")
@click.option(...)  # 参数定义
def ezviz_panel_gray(ctx, ...):
    # 主命令逻辑
    pass

def _panel_gray_operation(...):
    # 灰度操作逻辑
    pass
```

## 主要改进

### 1. 更好的用户体验

- ✅ **Dry-run 模式**：可以预览操作而不实际执行
- ✅ **进度显示**：批量操作时显示进度条
- ✅ **多种输出格式**：支持 text/json/yaml 输出
- ✅ **详细的帮助信息**：`--help` 提供完整的使用说明

### 2. 更健壮的错误处理

- ✅ **结构化异常**：使用 `CLIError` 进行统一错误处理
- ✅ **重试机制**：API 调用失败时自动重试
- ✅ **详细错误信息**：提供具体的错误原因和解决建议

### 3. 更好的可维护性

- ✅ **模块化设计**：功能分离，易于测试和维护
- ✅ **配置管理**：统一的配置文件系统
- ✅ **日志系统**：结构化的日志记录
- ✅ **类型提示**：完整的类型注解

### 4. 更强的扩展性

- ✅ **插件架构**：自动命令发现机制
- ✅ **配置层次**：支持多层级配置文件
- ✅ **环境变量支持**：可通过环境变量覆盖配置

## 迁移步骤

### 1. 安装新的 CLI 工具

```bash
# 如果还未安装
pip install -e .
```

### 2. 迁移配置

将原始脚本中的常量迁移到配置文件：

```yaml
# config.yaml
gray_accounts:
  - <EMAIL>
  - <EMAIL>
```

### 3. 更新调用方式

将原始的 Python 脚本调用替换为新的 CLI 命令：

```bash
# 原来
python your_script.py --name my-panel --type Gray

# 现在
ledvance ezviz-panel-gray --name my-panel --type Gray
```

### 4. 利用新功能

使用新命令提供的增强功能：

```bash
# 预览操作
ledvance --dry-run ezviz-panel-gray --name my-panel --type Gray

# JSON 输出
ledvance --output json ezviz-panel-gray --name my-panel --type Gray

# 详细日志
ledvance --log-level DEBUG ezviz-panel-gray --name my-panel --type Gray
```

## 兼容性说明

- ✅ **完全兼容**：所有原始功能都得到保留
- ✅ **参数兼容**：参数名称和行为完全一致
- ✅ **API 兼容**：使用相同的底层 API 调用
- ✅ **结果兼容**：产生相同的操作结果

## 建议的迁移策略

1. **并行运行**：在迁移期间，可以同时保留原始脚本和新命令
2. **逐步迁移**：先在测试环境中使用新命令，验证无误后再迁移生产环境
3. **培训团队**：确保团队成员了解新命令的使用方法
4. **更新文档**：更新相关的操作文档和流程

## 获得帮助

如果在迁移过程中遇到问题：

1. 查看命令帮助：`ledvance ezviz-panel-gray --help`
2. 使用 dry-run 模式验证操作
3. 检查日志输出以获取详细信息
4. 参考使用指南文档
