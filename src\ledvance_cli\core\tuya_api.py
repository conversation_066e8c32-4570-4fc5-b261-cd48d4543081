"""
涂鸦 API 客户端模块。

封装了与涂鸦 IoT 平台交互的逻辑，包括会话管理和 API 调用。
"""
import json
import logging
import os

import click
import requests
from requests import Response

from ledvance_cli import config as app_config
from ledvance_cli.commands.tuya_login import tuya_login
from ledvance_cli.core.exceptions import CLIError
from ledvance_cli.utils import retry, get_tuya_cookies_path

logger = logging.getLogger(__name__)

# 全局变量，用于缓存 cookies 和 headers
_cookies = None
_headers = None
COOKIES_FILE = "tuya-cookies.json"


def _login():
    """
    以编程方式调用 tuya-login 命令来刷新会话。
    """
    logger.info("会话无效或已过期，正在尝试重新登录...")
    try:
        ctx = click.get_current_context()
        ctx.invoke(tuya_login, output_file=COOKIES_FILE)
        logger.info("重新登录流程已完成。")
        return True
    except Exception as e:
        logger.error(f"执行登录脚本时出错: {e}")
        return False


def _load_cookies():
    """
    安全地从文件中加载 cookies。如果文件不存在或无效，则尝试登录。
    """
    cookies_path = get_tuya_cookies_path(COOKIES_FILE)
    if os.path.exists(cookies_path):
        try:
            with open(cookies_path, 'r', encoding='utf-8') as f:
                cookies = json.load(f)
                if cookies:
                    logger.debug("成功从文件加载 cookies。")
                    return cookies
        except (FileNotFoundError, json.JSONDecodeError) as e:
            logger.warning(f"加载 cookies 失败: {e}")

    logger.info(f"Cookies 文件 '{cookies_path}' 不存在或为空。")
    if _login():
        return _load_cookies()  # 递归调用以加载新生成的 cookie
    return {}


def get_cookies():
    """获取 cookies，使用延迟加载和缓存。"""
    global _cookies
    if _cookies is None:
        _cookies = _load_cookies()
    return _cookies


def get_headers():
    """获取 headers，使用延迟加载和缓存。"""
    global _headers
    if _headers is None:
        cookies = get_cookies()
        if not cookies:
            raise CLIError("无法加载 cookies，请先运行 'tuya-login' 命令。")
        _headers = {
            'Content-Type': 'application/json; charset=UTF-8',
            'Csrf-Token': cookies.get('csrf-token', '')
        }
    return _headers


def _is_session_invalid(response: Response):
    """检查响应是否表示会话无效。"""
    if not response.ok:
        return False
    try:
        data = response.json()
        return data.get('success') is False and data.get('errorCode') == 'USER_SESSION_INVALID'
    except json.JSONDecodeError:
        # 如果响应不是有效的 JSON，也可能意味着会话问题（例如，返回了 HTML 登录页面）
        return 'html' in response.headers.get('Content-Type', '').lower()


def _refresh_session_if_needed(response: Response):
    """如果会话无效，则刷新会话并重置全局缓存。"""
    if _is_session_invalid(response):
        logger.warning("检测到会话已失效，需要重新登录。")
        if _login():
            global _cookies, _headers
            _cookies = None
            _headers = None
            logger.info("会话已刷新，将重试原始请求。")
            return True
    return False


def res_success(res: Response):
    """检查响应是否成功。"""
    return res.ok and res.json().get('success')


@retry()
def page_product_list(payload: dict):
    """获取产品列表。"""
    logger.debug(f"正在获取产品列表， headers: {get_headers()}, payload: {payload}")
    host = app_config.APP_CONFIG.get("tuya_host")
    response = requests.post(f'{host}/micro-app/pmg/api/v4/product/list',
                             json=payload,
                             cookies=get_cookies(),
                             headers=get_headers())
    if _refresh_session_if_needed(response):
        return page_product_list(payload)

    if res_success(response):
        data = response.json()['result']
        return data.get('datas', []), data.get('totalCount', 0)
    else:
        logger.error(f'获取涂鸦产品列表失败, 错误: {response.text}')
        return [], 0


@retry()
def get_product_info(pid: str):
    """获取产品信息。"""
    host = app_config.APP_CONFIG.get("tuya_host")
    response = requests.post(f'{host}/micro-app/pmg/api/product/productGet',
                             cookies=get_cookies(),
                             headers=get_headers(),
                             json={'id': pid})
    if _refresh_session_if_needed(response):
        return get_product_info(pid)

    if res_success(response):
        return response.json()['result']
    else:
        logger.error(f'通过 pid [{pid}] 获取涂鸦产品信息失败, 错误: {response.text}')
        return None


@retry()
def get_product_dp(pid: str):
    """获取产品 DP 点。"""
    host = app_config.APP_CONFIG.get("tuya_host")
    response = requests.post(f'{host}/micro-app/pmg/api/product/schemaGetStd/V2',
                             cookies=get_cookies(),
                             headers=get_headers(),
                             json={'productId': pid})
    if _refresh_session_if_needed(response):
        return get_product_dp(pid)

    if res_success(response):
        return response.json()['result']['dps']
    else:
        logger.error(f'通过 pid [{pid}] 获取产品 DP 点失败, 错误: {response.text}')
        return []
