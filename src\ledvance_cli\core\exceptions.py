"""
CLI 应用程序的自定义异常。
"""

class CLIError(Exception):
    """所有特定于应用程序的错误的基异常。"""
    def __init__(self, message: str, exit_code: int = 1):
        super().__init__(message)
        self.exit_code = exit_code
        self.message = message

    def __str__(self) -> str:
        return self.message

class ConfigError(CLIError):
    """与配置相关的错误的异常。"""
    def __init__(self, message: str):
        super().__init__(f"配置错误: {message}", exit_code=2)

class CommandExecutionError(CLIError):
    """命令执行期间错误的异常。"""
    def __init__(self, command: str, stdout: str, stderr: str, exit_code: int):
        message = (
            f"执行命令 '{command}' 时出错。"
            f"退出代码: {exit_code}"
            f"标准输出: {stdout}"
            f"标准错误: {stderr}"
        )
        super().__init__(message, exit_code=exit_code)

