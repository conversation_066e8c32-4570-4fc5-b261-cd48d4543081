# Ledvance CLI 改进版 Dry-Run 实现指南

## 概述

本文档详细说明了Ledvance CLI工具改进后的dry-run实现模式。新的实现方式提供了更真实、更完整的命令执行体验，让用户能够验证完整的命令执行流程。

## 改进前后对比

### 原始实现（简单模式）
```python
def command_function(ctx: click.Context, param: str):
    is_dry_run = ctx.obj.dry_run
    
    if is_dry_run:
        logger.info("🔍 [DRY-RUN] 操作预览")
        logger.info("将要执行的操作...")
        return  # 直接返回，不执行任何逻辑
    
    # 实际命令逻辑
    actual_operation()
```

**问题：**
- 只显示操作预览，不执行实际逻辑
- 无法验证命令的完整执行路径
- 无法测试错误处理逻辑
- 用户无法看到真实的数据流转过程

### 改进实现（完整流程模式）
```python
def command_function(ctx: click.Context, param: str):
    is_dry_run = ctx.obj.dry_run
    
    logger.info("开始执行命令...")
    if is_dry_run:
        logger.info("🔍 [DRY-RUN] 运行在模拟模式")
    
    # 执行完整的命令流程
    if is_dry_run:
        logger.info("🔍 [DRY-RUN] 模拟API调用: some_api")
        result = mock_api_data()  # 使用模拟数据
    else:
        result = actual_api_call()  # 实际API调用
    
    # 继续处理逻辑...
    process_result(result)
    
    if is_dry_run:
        logger.info("🔍 [DRY-RUN] 模拟文件操作: 保存到文件")
    else:
        save_to_file(result)
```

**优势：**
- 执行完整的命令流程
- 在关键操作点使用模拟数据
- 保持真实的执行路径
- 可以测试错误处理逻辑

## 实现原则

### 1. 完整流程执行
- **不要**在函数开头检查dry-run后直接返回
- **要**让命令执行完整的业务逻辑流程
- **要**在每个关键操作点进行dry-run检查

### 2. 模拟数据策略
- **API调用**：使用结构化的模拟数据替代实际API调用
- **文件操作**：显示模拟操作日志，不实际创建/修改文件
- **网络请求**：跳过实际请求，返回模拟响应
- **数据库操作**：使用内存中的模拟数据

### 3. 日志标识规范
- 所有dry-run相关日志使用`🔍 [DRY-RUN]`前缀
- 模拟操作使用`模拟API调用:`、`模拟文件操作:`等描述
- 在命令结束时显示总结信息

### 4. 错误处理
- 在dry-run模式下也要测试错误处理逻辑
- 使用`模拟异常处理:`来显示错误处理过程
- 确保异常不会中断dry-run执行

## 已实现的命令示例

### 1. ezviz_panel_check.py
```python
# 配置读取
if is_dry_run:
    logger.info("🔍 [DRY-RUN] 读取配置文件中的产品列表...")
    # 提供模拟产品列表
    product_list = [mock_products...]
else:
    product_list = app_config.APP_CONFIG.get("panel_check_products", [])

# API调用
if is_dry_run:
    logger.info("🔍 [DRY-RUN] 模拟API调用: get_product_panel_version")
    version_list = [mock_version_data...]
else:
    version_list = ezviz_api.get_product_panel_version(...)

# 文件操作
if is_dry_run:
    logger.info(f"🔍 [DRY-RUN] 模拟文件操作: 写入文件 {output_file}")
else:
    with open(output_file, "w") as f:
        json.dump(data, f)
```

### 2. tuya_login.py
```python
# 浏览器操作
if is_dry_run:
    logger.info("🔍 [DRY-RUN] 模拟启动Chrome浏览器...")
    logger.info("🔍 [DRY-RUN] 模拟填充登录表单...")
    mock_cookies = [{"name": "sessionId", "value": "mock_session"}]
    cookie_dict = cookies_to_dict(mock_cookies)
else:
    driver = webdriver.Chrome(options=chrome_options)
    # 实际浏览器操作...
    cookies = driver.get_cookies()
    cookie_dict = cookies_to_dict(cookies)

# 文件保存
if is_dry_run:
    logger.info(f"🔍 [DRY-RUN] 模拟文件操作: 保存cookies到 {cookies_path}")
else:
    with open(cookies_path, 'w') as f:
        json.dump(cookie_dict, f)
```

### 3. tuya_dp_check.py
```python
# 产品列表获取
if is_dry_run:
    logger.info("🔍 [DRY-RUN] 模拟API调用: _fetch_all_products")
    products = [mock_products...]
else:
    products = _fetch_all_products(base_payload)

# DP检查循环
for product in products:
    if is_dry_run:
        logger.info(f"🔍 [DRY-RUN] 模拟API调用: get_product_dp for {product['id']}")
        dps = [mock_dp_data...]
    else:
        dps = tuya_api.get_product_dp(product['id'])
    
    # 继续处理逻辑...
```

## 模拟数据设计原则

### 1. 数据结构一致性
模拟数据必须与真实API返回的数据结构完全一致：

```python
# 真实API返回
{
    "productId": "TY_REAL_001",
    "rnName": "real-panel-name",
    "version": "V1.2.3",
    "appVersion": "2.0.0"
}

# 模拟数据
{
    "productId": "TY_LDV_mock_001",
    "rnName": "mock-panel-name",
    "version": "V1.0.0", 
    "appVersion": "2.0.0"
}
```

### 2. 数据多样性
提供多样化的模拟数据来测试不同场景：

```python
# 成功场景
mock_success_data = {...}

# 失败场景  
mock_error_data = None

# 边界场景
mock_empty_data = []
```

### 3. 随机性
在适当的地方引入随机性来模拟真实环境：

```python
import random

# 随机决定是否包含目标DP
has_target_dp = random.choice([True, False])
if not has_target_dp:
    mock_dps = [dp for dp in mock_dps if dp["code"] != target_dp_code]
```

## 测试验证

### 自动化测试
使用`scripts/test-dry-run.py`进行自动化测试：

```bash
python scripts/test-dry-run.py
```

测试覆盖：
- ✅ 所有6个命令的dry-run功能
- ✅ 帮助信息中包含--dry-run选项
- ✅ 检查dry-run输出格式的一致性
- ✅ 验证命令执行成功且无副作用

### 手动测试示例
```bash
# 测试完整流程
ledvance --dry-run ezviz-panel-check --env Test --type Gray

# 测试错误处理
ledvance --dry-run tuya-sync-dp --env Test --category invalid --pid invalid

# 测试文件操作
ledvance --dry-run tuya-login --output-file test.json
```

## 开发指南

### 为新命令添加改进版dry-run支持

1. **添加dry-run检查**
```python
@click.pass_context
def new_command(ctx: click.Context, param: str):
    is_dry_run = ctx.obj.dry_run
    
    logger.info("开始执行命令...")
    if is_dry_run:
        logger.info("🔍 [DRY-RUN] 运行在模拟模式")
```

2. **在API调用点添加模拟**
```python
if is_dry_run:
    logger.info("🔍 [DRY-RUN] 模拟API调用: api_name")
    result = create_mock_data()
else:
    result = actual_api_call()
```

3. **在文件操作点添加模拟**
```python
if is_dry_run:
    logger.info(f"🔍 [DRY-RUN] 模拟文件操作: 操作描述")
else:
    perform_file_operation()
```

4. **添加结束总结**
```python
if is_dry_run:
    logger.info("🔍 [DRY-RUN] 命令模拟完成 - 所有操作均为模拟")
```

### 最佳实践

1. **保持业务逻辑完整性**
   - 不要因为dry-run而跳过重要的业务逻辑
   - 确保模拟数据能够支持后续处理

2. **提供有意义的日志**
   - 详细描述每个模拟操作
   - 显示模拟数据的关键信息

3. **测试错误场景**
   - 在dry-run中也要测试异常处理
   - 确保错误不会中断执行

4. **保持一致性**
   - 使用统一的日志格式
   - 遵循相同的实现模式

## 总结

改进后的dry-run实现提供了：

✅ **完整的命令执行流程** - 不在开头直接返回
✅ **真实的业务逻辑验证** - 可以测试完整的执行路径  
✅ **详细的操作日志** - 清晰显示每个步骤
✅ **安全的模拟操作** - 无副作用但保持真实性
✅ **一致的用户体验** - 统一的输出格式和交互方式

这种实现方式大大提高了dry-run功能的实用性和可靠性，让用户能够在生产环境中安全地验证命令操作。
