"""
Ezviz 产品差异比较命令 - 专门用于比较不同环境间的产品差异。
"""
import logging

import click

from ledvance_cli.core import ezviz_api
from ledvance_cli.core.exceptions import CLIError
from ledvance_cli.utils import format_output, show_progress

logger = logging.getLogger(__name__)


@click.command("ezviz-product-diff")
@click.option(
    "--keyword",
    default="LDV_",
    type=str,
    help="产品关键字，用于过滤产品。"
)
@click.option(
    "--show-common",
    is_flag=True,
    help="同时显示两个环境都存在的产品。"
)
@click.pass_context
def ezviz_product_diff(
    ctx: click.Context,
    keyword: str,
    show_common: bool
):
    """
    Ezviz 产品比较。

    此命令支持：
    1. 比较生产环境和测试环境产品差异。
    2. 可选择显示两个环境都存在的产品。
    """
    output_format = ctx.obj.output_format

    logger.info(f"开始比较不同环境的产品差异 (关键字: {keyword})")

    try:
        base_payload = {
            'keyword': keyword
        }
        prod_env = 'Prod'
        test_env = 'Test'

        logger.info(f"正在获取生产环境产品 (关键字: {keyword})...")
        prod_products = ezviz_api.get_all_product(base_payload, prod_env)

        logger.info(f"正在获取测试环境产品 (关键字: {keyword})...")
        test_products = ezviz_api.get_all_product(base_payload, test_env)

        # 构建产品字典以便快速查找
        prod_dict = {p['productId']: p for p in prod_products}
        test_dict = {p['productId']: p for p in test_products}

        # 获取所有产品ID的并集
        all_product_ids = set(prod_dict.keys()) | set(test_dict.keys())

        logger.info(f"开始比较产品差异，共 {len(all_product_ids)} 个产品...")

        res = []
        # 使用进度条显示比较进度
        for pid in show_progress(all_product_ids, description="正在比较产品差异..."):
            # 产品只在生产环境存在
            if pid not in test_dict:
                product = prod_dict[pid]
                res.append({
                    'env': 'Prod Only',
                    'category': product.get('category', 'N/A'),
                    'pid': pid,
                    'name': product.get('productName', 'N/A'),
                    'status': '仅在生产环境存在'
                })
                continue

            # 产品只在测试环境存在
            if pid not in prod_dict:
                product = test_dict[pid]
                res.append({
                    'env': 'Test Only',
                    'category': product.get('category', 'N/A'),
                    'pid': pid,
                    'name': product.get('productName', 'N/A'),
                    'status': '仅在测试环境存在'
                })
                continue

            # 产品在两个环境都存在
            if show_common:
                product = prod_dict[pid]  # 使用生产环境的产品信息
                res.append({
                    'env': 'Both',
                    'category': product.get('category', 'N/A'),
                    'pid': pid,
                    'name': product.get('productName', 'N/A'),
                    'status': '两个环境都存在'
                })

        # 输出统计信息
        prod_only_count = sum(1 for item in res if item['env'] == 'Prod Only')
        test_only_count = sum(1 for item in res if item['env'] == 'Test Only')
        both_count = sum(1 for item in res if item['env'] == 'Both')

        if show_common:
            logger.info(f"比较完成: 仅生产环境 {prod_only_count} 个，仅测试环境 {test_only_count} 个，两环境共有 {both_count} 个")
        else:
            logger.info(f"比较完成: 仅生产环境 {prod_only_count} 个，仅测试环境 {test_only_count} 个")

        format_output(res, output_format, title=f"产品环境差异比较结果 (关键字: {keyword})")

    except CLIError as e:
        logger.error(f"产品比较时出错: {e}")
        ctx.exit(1)
    except Exception as e:
        logger.error(f"发生意外错误: {e}", exc_info=True)
        ctx.exit(1)

    logger.info("产品比较操作完成。")
