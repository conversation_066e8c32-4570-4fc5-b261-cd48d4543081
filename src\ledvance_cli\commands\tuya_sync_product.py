"""
将涂鸦产品同步到 Ezviz 平台的命令。
"""
import logging

import click

from ledvance_cli import config as app_config
from ledvance_cli.core import ezviz_api, tuya_api
from ledvance_cli.core.exceptions import CLIError
from ledvance_cli.utils import download_picture, show_progress

logger = logging.getLogger(__name__)


def _download_and_upload_pic(icon_path: str, env: str) -> str | None:
    """下载涂鸦图片并上传到 Ezviz。"""
    temp_dir = app_config.APP_CONFIG.get("temp_img_dir")
    tuya_img_host = app_config.APP_CONFIG.get("tuya_img_host")
    
    pic_path = download_picture(f'{tuya_img_host}/{icon_path}', temp_dir)
    if pic_path is None:
        logger.error("下载涂鸦产品图片失败。")
        return None
    
    # 注意：upload_file 也需要 env 参数
    uploaded_url = ezviz_api.upload_file(pic_path, env=env)
    if uploaded_url is None:
        logger.error("上传图片到 Ezviz 失败。")
        return None
        
    return uploaded_url


def _create_ez_product(ty_product: dict, category_map: dict, args: dict, pic_url: str) -> bool:
    """在 Ezviz 平台创建产品。"""
    create_payload = {
        "id": category_map[args['category']],
        "accessType": "[0]",
        "category": args['category'],
        "mainCategory": args['main_category'],
        "minAppVersion": '1.0.0',
        "pid": category_map[args['main_category']],
        "productName": ty_product['name'],
        "productModel": args['category'],
        "transType": 3,
        "remark": args['remark'],
        "productId": f'TY_LDV_{ty_product["id"]}',
        "networkType": 'bluetooth',
        "productPic": pic_url,
        "mainCategoryId": category_map[args['main_category']],
        "categoryId": category_map[args['category']],
        "isTemplate": 0,
        "ext": '[]'
    }
    return ezviz_api.create_product(create_payload, env=args['env'])


def _deal_with_publish(pid: str, status: str, env: str):
    """处理产品的发布状态变更。"""
    publish_param = {
        'pId': pid,
        'firmwareUpgradeCode': 'V1.0.0 build 201231',
        'publishStatus': status
    }
    res = ezviz_api.publish_product(publish_param, env=env)
    logger.info(f"将产品 {pid} 状态变更为 '{status}' 的结果: {res}")


@click.command("tuya-sync-product")
@click.option("--env", default="Prod", type=click.Choice(['Prod', 'Test'], case_sensitive=True), help="要操作的 Ezviz 平台环境。")
@click.option("--main-category", required=True, help="Ezviz 平台的一级品类 ID。")
@click.option("--category", required=True, help="Ezviz 平台的二级品类 ID。")
@click.option("--rn-name", required=True, help="要绑定的 RN 面板名称。")
@click.option("--remark", default="", help="产品的备注信息。")
@click.option("--pids", required=True, type=str, help="要同步的涂鸦产品 PID 列表，以逗号(,)分隔。")
@click.pass_context
def tuya_sync_product(ctx: click.Context, env: str, main_category: str, category: str, rn_name: str, remark: str, pids: str):
    """
    将一个或多个涂鸦产品同步到指定的 Ezviz 环境。

    此命令会自动执行以下步骤：
    1. 检查产品是否已存在于 Ezviz。
    2. 如果不存在，则从涂鸦获取产品信息。
    3. 在 Ezviz 上创建新产品（包括上传图片）。
    4. 为新产品添加标准功能点 (DPs)。
    5. 为新产品绑定 UI 面板。
    6. 改变产品的发布状态。
    如果在任何步骤失败，它将尝试回滚（删除已创建的 Ezviz 产品）。
    """
    is_dry_run = ctx.obj.dry_run
    pid_list = [pid.strip() for pid in pids.split(',') if pid.strip()]

    if is_dry_run:
        logger.info("🔍 [DRY-RUN] 涂鸦产品同步操作预览")
        logger.info(f"🌐 目标环境: {env}")
        logger.info(f"📂 一级品类: {main_category}")
        logger.info(f"📁 二级品类: {category}")
        logger.info(f"🎨 RN面板名称: {rn_name}")
        logger.info(f"📝 备注信息: {remark if remark else '无'}")
        logger.info(f"🎯 产品列表: {', '.join(pid_list)}")
        logger.info("🔄 将要执行的操作:")
        logger.info("  1. 检查产品是否已存在于Ezviz")
        logger.info("  2. 从涂鸦获取产品信息")
        logger.info("  3. 下载并上传产品图片")
        logger.info("  4. 在Ezviz创建新产品")
        logger.info("  5. 添加模板功能到产品")
        logger.info("  6. 绑定RN面板")
        logger.info("  7. 发布产品")
        logger.info("💡 注意: 在dry-run模式下，不会进行实际的API调用和数据修改")
        return

    args = {
        "env": env,
        "main_category": main_category,
        "category": category,
        "rn_name": rn_name,
        "remark": remark
    }

    logger.info(f"开始同步涂鸦产品到 Ezviz '{env}' 环境...")

    try:
        # 这些信息对于所有 PID 都是共享的，先获取
        category_map = ezviz_api.get_category_map(env=env)
        if not category_map:
            raise CLIError("无法从 Ezviz 获取品类映射表。")

        template_features = ezviz_api.get_template_features(args['category'], env=env)
        if not template_features:
            raise CLIError(f"无法从 Ezviz 获取品类 '{args['category']}' 的模板功能。")

    except CLIError as e:
        logger.error(f"准备阶段失败: {e}")
        return

    for pid in show_progress(pid_list, description="正在同步产品..."):
        ez_pid = f'TY_LDV_{pid}'
        logger.info(f"--- 开始处理 PID: {pid} (Ezviz PID: {ez_pid}) ---")
        
        try:
            # 1. 检查产品是否已存在
            if ezviz_api.get_product_info(ez_pid, env=env):
                logger.warning(f"产品 {ez_pid} 已存在于 Ezviz，跳过。")
                continue

            # 2. 获取涂鸦产品信息
            ty_product = tuya_api.get_product_info(pid)
            if not ty_product:
                logger.error(f"在涂鸦平台找不到 PID 为 {pid} 的产品，跳过。")
                continue
            
            # 3. 创建产品 (包括图片上传)
            pic_url = _download_and_upload_pic(ty_product["icon"], env=env)
            if not pic_url:
                raise CLIError("下载或上传产品图片失败。")

            if not _create_ez_product(ty_product, category_map, args, pic_url):
                raise CLIError("在 Ezviz 创建产品失败。")
            logger.info(f"在 Ezviz 创建产品 {ez_pid} 成功。")

            # 4. 添加模板功能能
            if not ezviz_api.add_template_features_to_product(ez_pid, template_features, env=env):
                raise CLIError("为产品添加模板功能失败。")
            logger.info(f"为产品 {ez_pid} 添加模板功能成功。")

            # 5. 绑定面板
            if args['rn_name'] == 'tuyapanel':
                panel_list = [{ 'panelId': 'tuyapanel', 'panelType': 2, 'appVersion': "1.0.0" }]
            else:
                panel_list = ezviz_api.list_panel_by_name(args['rn_name'], env=env)
            if not panel_list:
                raise CLIError(f"找不到名为 '{args['rn_name']}' 的 RN 面板。")
            
            panel_info = {'pid': ez_pid, 'panelId': panel_list[0]['panelId'], 'panelType': panel_list[0]['panelType'], 'appVersion': panel_list[0]['appVersion']}
            if not ezviz_api.set_product_panel(panel_info, env=env):
                raise CLIError("为产品绑定面板失败。")
            logger.info(f"为产品 {ez_pid} 绑定面板 '{args['rn_name']}' 成功。")

            # 6. 发布产品
            _deal_with_publish(ez_pid, 'trial_produce_ing', env=env)
            _deal_with_publish(ez_pid, 'trial_produce_ed', env=env)
            
            logger.info(f"--- PID: {pid} 同步成功 ---")

        except CLIError as e:
            logger.error(f"处理 PID {pid} 时发生错误: {e}")
            logger.info(f"正在尝试为 {ez_pid} 执行清理/回滚...")
            # 尝试回滚
            if ezviz_api.get_product_info(ez_pid, env=env):
                delete_res = ezviz_api.delete_product(ez_pid, env=env)
                logger.info(f"删除产品 {ez_pid} 的结果: {delete_res}")
            else:
                logger.info(f"产品 {ez_pid} 未创建或已被删除，无需清理。")
            continue # 继续处理下一个 PID

    logger.info("所有产品同步任务完成。")
