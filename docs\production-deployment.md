# Ledvance CLI 生产环境部署指南

本指南详细说明如何在生产环境中部署和分发Ledvance CLI工具。

## 📦 部署方式概览

| 部署方式 | 适用场景 | 优点 | 缺点 |
|---------|---------|------|------|
| **直接安装** | 单机部署 | 简单快速 | 需要源码 |
| **Wheel包分发** | 团队分发 | 离线安装 | 需要构建 |
| **虚拟环境** | 隔离部署 | 环境隔离 | 管理复杂 |
| **Docker容器** | 容器化部署 | 环境一致 | 资源开销 |

## 🚀 快速部署

### 方法1: 一键安装脚本

**Windows:**
```batch
# 下载项目
git clone <repository-url>
cd ledvance-cli

# 运行安装脚本
scripts\install-production.bat
```

**Linux/macOS:**
```bash
# 下载项目
git clone <repository-url>
cd ledvance-cli

# 运行安装脚本
./scripts/install-production.sh
```

**跨平台Python脚本:**
```bash
python scripts/install-production.py
```

### 方法2: 手动安装

```bash
# 1. 进入项目目录
cd ledvance-cli

# 2. 安装CLI工具（生产模式）
pip install .

# 3. 验证安装
ledvance --version
python scripts/verify-installation.py
```

## 📦 构建分发包

### 构建Wheel包

```bash
# 使用构建脚本（推荐）
python scripts/build-package.py

# 或手动构建
pip install build
python -m build
```

构建完成后，在`dist/`目录下会生成：
- `ledvance_cli-0.1.0-py3-none-any.whl` - Wheel包
- `ledvance_cli-0.1.0.tar.gz` - 源码包

### 分发给用户

```bash
# 用户安装wheel包
pip install ledvance_cli-0.1.0-py3-none-any.whl

# 或从源码包安装
pip install ledvance_cli-0.1.0.tar.gz
```

## 🏢 企业部署方案

### 1. 共享目录部署

```bash
# 1. 构建包
python scripts/build-package.py

# 2. 复制到共享目录
cp dist/ledvance_cli-*.whl /shared/packages/

# 3. 用户安装
pip install /shared/packages/ledvance_cli-*.whl
```

### 2. 内网PyPI服务器

```bash
# 1. 上传到内网PyPI
twine upload --repository-url http://internal-pypi.company.com dist/*

# 2. 用户安装
pip install -i http://internal-pypi.company.com ledvance-cli
```

### 3. 批量部署脚本

创建批量部署脚本：

```bash
#!/bin/bash
# deploy-to-servers.sh

SERVERS=("server1" "server2" "server3")
WHEEL_FILE="ledvance_cli-0.1.0-py3-none-any.whl"

for server in "${SERVERS[@]}"; do
    echo "部署到 $server..."
    scp dist/$WHEEL_FILE $server:/tmp/
    ssh $server "pip install /tmp/$WHEEL_FILE"
    ssh $server "ledvance --version"
done
```

## 🐳 Docker部署

### Dockerfile

```dockerfile
FROM python:3.9-slim

# 设置工作目录
WORKDIR /app

# 复制项目文件
COPY . .

# 安装CLI工具
RUN pip install .

# 创建非root用户
RUN useradd -m ledvance
USER ledvance

# 设置入口点
ENTRYPOINT ["ledvance"]
```

### 构建和运行

```bash
# 构建镜像
docker build -t ledvance-cli .

# 运行容器
docker run --rm ledvance-cli --help

# 挂载配置文件
docker run --rm -v $(pwd)/config.yaml:/app/config.yaml ledvance-cli tuya-login
```

## 🔧 环境配置

### 系统要求

- **Python**: 3.8+
- **操作系统**: Windows 10+, macOS 10.15+, Linux (Ubuntu 18.04+)
- **内存**: 最少512MB
- **磁盘**: 最少100MB

### 依赖管理

生产环境建议锁定依赖版本：

```bash
# 生成requirements.txt
pip freeze > requirements-prod.txt

# 安装固定版本依赖
pip install -r requirements-prod.txt
```

### 配置文件管理

```bash
# 1. 创建配置目录
mkdir -p /etc/ledvance

# 2. 复制配置文件
cp config.yaml.example /etc/ledvance/config.yaml

# 3. 设置环境变量
export LEDVANCE_CONFIG_PATH=/etc/ledvance/config.yaml
```

## 🔍 部署验证

### 自动验证

```bash
# 运行验证脚本
python scripts/verify-installation.py

# 检查特定功能
ledvance --version
ledvance --help
ledvance completion bash
```

### 手动验证清单

- [ ] 命令可用性：`ledvance --version`
- [ ] 帮助系统：`ledvance --help`
- [ ] 子命令：`ledvance tuya-login --help`
- [ ] 自动补全：`ledvance completion bash`
- [ ] 配置加载：检查配置文件路径
- [ ] 依赖包：验证所有依赖可用

## 🚨 故障排除

### 常见问题

**1. 命令未找到**
```bash
# 检查安装路径
pip show ledvance-cli

# 检查PATH环境变量
echo $PATH

# 使用完整路径
python -m ledvance_cli.cli --help
```

**2. 权限错误**
```bash
# 使用用户安装
pip install --user .

# 或使用虚拟环境
python -m venv venv
source venv/bin/activate
pip install .
```

**3. 依赖冲突**
```bash
# 使用虚拟环境隔离
python -m venv fresh-env
source fresh-env/bin/activate
pip install .
```

**4. 配置文件问题**
```bash
# 检查配置文件位置
ledvance --verbose tuya-login --help

# 创建默认配置
cp config.yaml.example config.yaml
```

### 日志调试

```bash
# 启用详细日志
ledvance --log-level DEBUG --verbose tuya-login

# 检查日志文件
tail -f app.log
```

## 📊 监控和维护

### 健康检查

创建健康检查脚本：

```bash
#!/bin/bash
# health-check.sh

if ledvance --version > /dev/null 2>&1; then
    echo "✅ Ledvance CLI 正常"
    exit 0
else
    echo "❌ Ledvance CLI 异常"
    exit 1
fi
```

### 定期维护

```bash
# 更新CLI工具
pip install --upgrade ledvance-cli

# 清理临时文件
rm -rf /tmp/ledvance_*

# 检查配置文件
ledvance --help > /dev/null
```

## 🔄 更新和回滚

### 更新流程

```bash
# 1. 备份当前版本
pip freeze | grep ledvance-cli > current-version.txt

# 2. 安装新版本
pip install --upgrade ledvance-cli

# 3. 验证新版本
python scripts/verify-installation.py

# 4. 如果有问题，回滚
pip install ledvance-cli==<old-version>
```

### 版本管理

```bash
# 查看可用版本
pip index versions ledvance-cli

# 安装特定版本
pip install ledvance-cli==0.1.0

# 查看当前版本
ledvance --version
```

## 📚 相关文档

- [安装指南](installation.md)
- [Shell自动补全](shell-completion.md)
- [配置说明](../config.yaml.example)
- [开发指南](../README.md)
