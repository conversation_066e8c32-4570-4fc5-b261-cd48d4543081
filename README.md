# 现代化 Python CLI 应用程序模板

该存储库包含一个现代化的、生产就绪的 Python CLI 应用程序，可用作未来项目的模板。

## 特性

- **命令行界面:** 使用 `click` 构建。
- **Shell自动补全:** 支持 Bash、Zsh、Fish 和 PowerShell 的 Tab 键自动补全。
- **配置:** 支持 YAML 配置文件 (`config.yaml`)、环境变量和 `.env` 文件。
- **日志记录:** 使用 `rich` 提供全面的日志记录，可输出精美的格式并支持日志文件轮换。
- **可扩展性:** 插件式架构，可轻松添加新命令。
- **代码质量:** 遵循 PEP 8 规范，包含类型提示和文档字符串。
- **错误处理:** 强大的异常处理机制，支持可配置的重试。
- **打包:** 使用 `pyproject.toml` 和 `setuptools` 进行打包。

## 安装

### 🚀 快速安装（生产环境）

**推荐方式 - 使用安装脚本:**

```bash
# Windows
scripts/install-production.bat

# Linux/macOS
scripts/install-production.sh

# 或使用Python脚本（跨平台）
python scripts/install-production.py
```

**手动安装:**

```bash
# 1. 克隆或下载项目
git clone https://github.com/your-username/ledvance-cli.git
cd ledvance-cli

# 2. 安装CLI工具（生产模式）
pip install .

# 3. 验证安装
ledvance --version
```

### 🔧 开发环境安装

1.  **克隆存储库:**
    ```bash
    git clone https://github.com/your-username/ledvance-cli.git
    cd ledvance-cli
    ```

2.  **创建并激活虚拟环境:**
    ```bash
    python -m venv venv
    source venv/bin/activate  # Linux/macOS
    # 或 venv\Scripts\activate  # Windows
    ```

3.  **安装依赖:**
    ```bash
    pip install -r requirements.txt
    ```

4.  **以开发模式安装 CLI:**
    ```bash
    pip install -e .
    ```

5.  **安装Shell自动补全（可选）:**
    ```bash
    # 自动检测并安装补全
    python scripts/install-completion.py

    # 或手动为特定shell安装
    ledvance completion bash --install     # Bash
    ledvance completion zsh --install      # Zsh
    ledvance completion fish --install     # Fish
    ledvance completion powershell --install  # PowerShell
    ```

### 📦 从构建包安装

```bash
# 1. 构建分发包
python scripts/build-package.py

# 2. 安装构建的包
pip install dist/ledvance_cli-*.whl
```

详细安装指南请参考：[安装文档](docs/installation.md)

## 配置

1.  **环境变量:**
    将示例文件 `.env.example` 复制为 `.env` 并更新其中的变量：
    ```bash
    cp .env.example .env
    ```

2.  **YAML 配置:**
    将示例文件 `config.yaml.example` 复制为 `config.yaml` 并自定义设置：
    ```bash
    cp config.yaml.example config.yaml
    ```

## 使用

CLI 的主入口点是 `ledvance`。

```bash
ledvance --help
```

### 可用选项

- `--config-file`: 配置文件的路径。
- `--log-level`: 日志记录级别 (DEBUG, INFO, WARNING, ERROR)。
- `--output`: 输出格式 (text, json, yaml)。
- `--dry-run`: 执行空运行，不进行实际更改。
- `--verbose`: 启用详细输出。

### Dry-Run 功能

所有命令都支持 `--dry-run` 参数，允许您预览将要执行的操作而不实际执行：

```bash
# 预览涂鸦平台登录操作
ledvance --dry-run tuya-login

# 预览产品同步操作
ledvance --dry-run tuya-sync-product --env Test --main-category 123 --category 456 --rn-name panel --pids TY001

# 预览面板升级操作
ledvance --dry-run ezviz-panel-upgrade --env Test --type Upgrade --modules module1

# 预览面板创建操作
ledvance --dry-run ezviz-panel-create --name "测试面板" --rn-name "test-panel"

# 预览面板设置操作
ledvance --dry-run ezviz-panel-set --name "test-panel" --pids "TY_LDV_001,TY_LDV_002"
```

详细说明请参考：[Dry-Run功能指南](docs/dry-run-guide.md)

### 示例命令

```bash
# 涂鸦平台登录
ledvance tuya-login

# 同步产品到 Ezviz
ledvance tuya-sync-product --env prod --pid TY001,TY002

# 检查面板版本
ledvance ezviz-panel-check --env prod --type gray --app-version 2.0.16

# 使用 JSON 文件指定产品列表
ledvance ezviz-panel-check --products products.json --output json

# 面板升级流程
ledvance ezviz-panel-upgrade --modules "module1,module2" --env test

# 面板灰度操作
ledvance ezviz-panel-upgrade --modules "module1" --type gray

# 创建新面板
ledvance ezviz-panel-create --name "我的面板" --rn-name "my-panel-rn" --category "LightSource"

# 创建自定义面板
ledvance ezviz-panel-create --type Custom --name "自定义面板" --rn-name "custom-panel" --pid "TY_LDV_123"

# 为多个产品设置面板
ledvance ezviz-panel-set --name "my-panel" --pids "TY_LDV_001,TY_LDV_002,TY_LDV_003"

# 检查数据点
ledvance tuya-dp-check --pid TY001
```

## Shell 自动补全

Ledvance CLI 支持智能的 Tab 键自动补全功能，让您更高效地使用命令行工具。

### 快速启用

```bash
# 自动检测shell并安装补全
python scripts/install-completion.py

# 重新启动终端或运行
source ~/.bashrc  # Bash
source ~/.zshrc   # Zsh
```

### 使用方法

```bash
# 补全命令名称
ledvance <TAB>
# 显示: ezviz-panel-check  ezviz-panel-create  ezviz-panel-set  ezviz-panel-upgrade  tuya-dp-check  tuya-login  tuya-sync-product

# 补全部分命令
ledvance tuya-<TAB>
# 显示: tuya-dp-check  tuya-login  tuya-sync-product

# 补全选项参数
ledvance tuya-sync-product --<TAB>
# 显示: --env  --main-category  --category  --rn-name  --remark  --help

# 补全参数值
ledvance --log-level <TAB>
# 显示: DEBUG  INFO  WARNING  ERROR  CRITICAL
```

详细说明请参考：[Shell自动补全指南](docs/shell-completion.md)

## 可用命令

### ezviz-panel-check
检查 Ezviz 面板版本信息。

**选项:**
- `--env`: 环境 (Prod/Test)
- `--type`: 检查类型 (gray/normal)
- `--app-version`: App 版本号
- `--output-dir`: 输出目录
- `--products`: 产品列表 (JSON 字符串或文件路径)

**示例:**
```bash
# 使用默认配置检查
ledvance ezviz-panel-check

# 指定环境和类型
ledvance ezviz-panel-check --env test --type normal

# 使用 JSON 文件
ledvance ezviz-panel-check --products products.json
```

### ezviz-panel-create
创建新的 Ezviz 面板。

**选项:**
- `--env`: 环境 (Prod/Test)
- `--type`: 面板类型 (Standard/Custom)
- `--name`: 面板名称 (必需)
- `--rn-name`: RN包名 (必需)
- `--version`: RN版本 (默认: V1.0.0)
- `--app-version`: App版本 (默认: 1.0.0)
- `--desc`: 描述
- `--category`: 一级品类
- `--sub-category`: 二级品类
- `--pid`: 产品ID (自定义面板必需)
- `--base`: 基于面板名称
- `--dir`: 包所在目录

**示例:**
```bash
# 创建标准面板
ledvance ezviz-panel-create --name "智能灯泡面板" --rn-name "smart-bulb-panel" --category "LightSource"

# 创建自定义面板
ledvance ezviz-panel-create --type Custom --name "自定义面板" --rn-name "custom-panel" --pid "TY_LDV_123456"

# 指定详细信息
ledvance ezviz-panel-create --name "高级面板" --rn-name "advanced-panel" --version "V2.0.0" --app-version "2.0.16" --desc "高级功能面板"

# 预览创建操作
ledvance --dry-run ezviz-panel-create --name "测试面板" --rn-name "test-panel"
```

### ezviz-panel-set
为多个产品批量设置面板。

**选项:**
- `--env`: 环境 (Prod/Test)
- `--name`: RN包名 (必需)
- `--pids`: 产品ID列表，用逗号分隔 (必需)

**示例:**
```bash
# 为多个产品设置面板
ledvance ezviz-panel-set --name "smart-bulb-panel" --pids "TY_LDV_001,TY_LDV_002,TY_LDV_003"

# 在生产环境设置面板
ledvance ezviz-panel-set --env Prod --name "production-panel" --pids "TY_LDV_prod001,TY_LDV_prod002"

# 预览设置操作
ledvance --dry-run ezviz-panel-set --name "test-panel" --pids "TY_LDV_test001,TY_LDV_test002"

# JSON 格式输出
ledvance --output json ezviz-panel-set --name "panel-name" --pids "TY_LDV_001,TY_LDV_002"
```

### ezviz-panel-upgrade
完整的 Ezviz 面板升级流程。

**选项:**
- `--env`: 环境 (Prod/Test)
- `--type`: 操作类型 (upgrade/gray/release)
- `--dir`: 包所在目录
- `--version`: 面板版本 (可选，自动递增)
- `--app-version`: 最低 app 版本
- `--users`: 灰度账号列表 (逗号分隔)
- `--modules`: 模块名称列表 (逗号分隔)

**示例:**
```bash
# 完整升级流程
ledvance ezviz-panel-upgrade --modules "module1,module2"

# 仅灰度操作
ledvance ezviz-panel-upgrade --modules "module1" --type gray

# 指定版本和用户
ledvance ezviz-panel-upgrade --modules "module1" --version "2.0.0" --users "<EMAIL>"

# 发布操作
ledvance ezviz-panel-upgrade --modules "module1" --type release
```

### tuya-sync-product
将涂鸦产品同步到 Ezviz 平台。

### tuya-login
登录涂鸦平台并保存会话 cookies。

### tuya-dp-check
检查产品的数据点配置。

## 添加新命令

要添加新命令，请在 `src/ledvance_cli/commands` 目录中创建一个新的 Python 文件。该文件必须包含一个用 `click.command()` 装饰的函数。CLI 将自动发现并加载它。
