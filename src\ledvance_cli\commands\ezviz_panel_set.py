"""
Ezviz 面板设置命令 - 为多个产品设置面板
"""

import click
from typing import List, Optional

from ledvance_cli.core.exceptions import CLIError
from ledvance_cli.core import ezviz_api
from ledvance_cli.utils import logger, show_progress, format_output


@click.command("ezviz-panel-set")
@click.option(
    "--env",
    default="Test",
    type=click.Choice(['Prod', 'Test'], case_sensitive=True),
    help="要操作的平台环境：'Prod' (生产) 或 'Test' (测试)。"
)
@click.option(
    "--name",
    required=True,
    help="RN包名。"
)
@click.option(
    "--pids",
    required=True,
    help="产品ID列表，用逗号分隔。"
)
@click.pass_context
def ezviz_panel_set(ctx, env: str, name: str, pids: str):
    """
    Ezviz 面板设置命令 - 为多个产品设置面板。
    
    此命令支持为多个产品批量设置面板：
    1. 根据RN包名查找面板信息
    2. 为每个产品设置指定的面板
    3. 显示处理进度和结果
    """
    is_dry_run = ctx.obj.dry_run
    output_format = ctx.obj.output_format
    
    # 解析产品ID列表
    pid_list = [pid.strip() for pid in pids.split(',') if pid.strip()]
    if not pid_list:
        raise CLIError("产品ID列表不能为空")
    
    # 转换环境参数
    env_lower = env.lower()
    
    if is_dry_run:
        logger.info("🔍 [DRY-RUN] Ezviz面板设置操作预览")
        logger.info(f"🌐 目标环境: {env}")
        logger.info(f"📦 RN包名: {name}")
        logger.info(f"🆔 产品ID列表: {', '.join(pid_list)}")
        logger.info(f"📊 产品数量: {len(pid_list)}")
        logger.info("🔄 将要执行的操作:")
        logger.info("  1. 根据RN包名查找面板信息")
        logger.info("  2. 为每个产品设置面板")
        logger.info("💡 注意: 在dry-run模式下，不会进行实际的API调用")
        
        # 在 dry-run 模式下也执行完整流程以显示详细步骤
        logger.info("🔄 开始执行 dry-run 模拟流程...")
    
    logger.info("开始面板设置操作")
    logger.info(f"环境: {env}, RN包名: {name}")
    logger.info(f"产品数量: {len(pid_list)}")
    
    try:
        # 1. 查找面板信息
        logger.info(f"查找面板 '{name}' 的信息...")
        
        if is_dry_run:
            logger.info("🔍 [DRY-RUN] 模拟查找面板信息")
            panel_list = [{
                'panelId': f'mock_panel_id_{name}',
                'panelType': 1,
                'appVersion': '1.0.0'
            }]
        else:
            panel_list = ezviz_api.list_panel_by_name(name, env=env_lower)
        
        if not panel_list or len(panel_list) == 0:
            raise CLIError(f"面板 [{name}] 列表为空或未找到")
        
        panel_info_template = {
            'panelId': panel_list[0]['panelId'],
            'panelType': panel_list[0]['panelType'],
            'appVersion': panel_list[0]['appVersion'],
        }
        
        logger.info(f"找到面板信息: ID={panel_info_template['panelId']}, "
                   f"类型={panel_info_template['panelType']}, "
                   f"App版本={panel_info_template['appVersion']}")
        
        # 2. 为每个产品设置面板
        results = []
        success_count = 0
        failed_pids = []
        
        for pid in show_progress(pid_list, description="正在设置产品面板..."):
            logger.info(f"为产品 [{pid}] 设置面板...")
            
            panel_info = {
                **panel_info_template,
                'pid': pid
            }
            
            try:
                if is_dry_run:
                    logger.info(f"🔍 [DRY-RUN] 模拟为产品 {pid} 设置面板")
                    result = True
                else:
                    result = ezviz_api.set_product_panel(panel_info, env=env_lower)
                
                if result:
                    logger.info(f"✅ 产品 [{pid}] 面板设置成功")
                    success_count += 1
                else:
                    logger.error(f"❌ 产品 [{pid}] 面板设置失败")
                    failed_pids.append(pid)
                
                results.append({
                    'pid': pid,
                    'success': result,
                    'panel_name': name
                })
                
            except Exception as e:
                logger.error(f"❌ 产品 [{pid}] 面板设置异常: {e}")
                failed_pids.append(pid)
                results.append({
                    'pid': pid,
                    'success': False,
                    'error': str(e),
                    'panel_name': name
                })
        
        # 3. 输出结果摘要
        logger.info("=" * 60)
        logger.info("📊 面板设置结果摘要:")
        logger.info(f"✅ 成功: {success_count}/{len(pid_list)}")
        
        if failed_pids:
            logger.info(f"❌ 失败: {len(failed_pids)}")
            logger.info(f"失败的产品ID: {', '.join(failed_pids)}")
        
        format_output(results, output_format, title=f"面板设置结果 ({env.upper()})")
        
        logger.info("面板设置操作完成。")
        
        # 如果有失败的情况，返回非零退出码
        if failed_pids:
            raise CLIError(f"有 {len(failed_pids)} 个产品面板设置失败")
            
    except CLIError:
        raise
    except Exception as e:
        logger.error(f"面板设置操作失败: {e}")
        raise CLIError(f"面板设置操作失败: {e}")
