"""
Ezviz 面板灰度命令 - 专门用于面板灰度操作。
"""
import logging
from typing import List, Optional

import click

from ledvance_cli import config as app_config
from ledvance_cli.core import ezviz_api
from ledvance_cli.core.exceptions import CLIError
from ledvance_cli.utils import format_output, show_progress

logger = logging.getLogger(__name__)


@click.command("ezviz-panel-gray")
@click.option(
    "--env",
    default="Test",
    type=click.Choice(['Prod', 'Test'], case_sensitive=True),
    help="要操作的平台环境：'Prod' (生产) 或 'Test' (测试)。"
)
@click.option(
    "--type",
    "gray_type",
    default="Gray",
    type=click.Choice(['Gray', 'Release'], case_sensitive=True),
    help="灰度类型：'Gray' (设置灰度) 或 'Release' (取消灰度，发布)。"
)
@click.option(
    "--name",
    required=True,
    help="RN包名称。"
)
@click.option(
    "--pids",
    help="产品ID列表，用逗号分隔。如果未指定，则对整个面板进行灰度操作。"
)
@click.option(
    "--users",
    help="灰度账号列表，用逗号分隔。如果未指定，使用配置中的默认账号。"
)
@click.pass_context
def ezviz_panel_gray(
    ctx: click.Context,
    env: str,
    gray_type: str,
    name: str,
    pids: Optional[str],
    users: Optional[str]
):
    """
    Ezviz 面板灰度命令 - 专门用于面板灰度操作。

    此命令支持：
    1. 设置面板灰度用户
    2. 为特定产品设置灰度
    3. 取消灰度（发布面板）
    """
    output_format = ctx.obj.output_format
    is_dry_run = ctx.obj.dry_run

    if is_dry_run:
        logger.info("🔍 [DRY-RUN] Ezviz面板灰度操作预览")
        logger.info(f"🌐 目标环境: {env}")
        logger.info(f"🔧 灰度类型: {gray_type}")
        logger.info(f"📦 RN包名称: {name}")
        logger.info(f"🏷️ 产品ID列表: {pids if pids else '全部产品'}")
        logger.info(f"👥 灰度用户: {users if users else '使用配置默认值'}")
        logger.info("🔄 将要执行的操作:")
        logger.info("  1. 查找指定的面板")
        logger.info("  2. 解析用户和产品列表")
        logger.info("  3. 执行灰度操作")
        logger.info("💡 注意: 在dry-run模式下，不会进行实际的API调用")
        return

    logger.info(f"开始面板灰度操作 (环境: {env}, 类型: {gray_type}, 面板: {name})")

    try:
        # 解析用户列表
        user_list = []
        if users:
            user_list = [user.strip() for user in users.split(',') if user.strip()]
            logger.info(f"指定的灰度用户列表: {user_list}")
        else:
            user_list = app_config.APP_CONFIG.get("gray_accounts", [])
            if user_list:
                logger.info(f"使用配置中的默认灰度用户列表: {user_list}")

        # 解析产品ID列表
        pid_list = []
        if pids:
            pid_list = [pid.strip() for pid in pids.split(',') if pid.strip()]
            logger.info(f"指定的产品ID列表: {pid_list}")

        # 查找面板
        panel_list = ezviz_api.list_panel_by_name(name, env=env)
        if not panel_list:
            raise CLIError(f"面板 [{name}] 未找到")

        panel = panel_list[0]
        panel_id = panel['panelId']
        logger.info(f"找到面板: {name} (ID: {panel_id})")

        # 执行灰度操作
        results = []
        if pid_list:
            # 为特定产品设置灰度
            total_pids = len(pid_list)
            for index, pid in enumerate(show_progress(pid_list, description="正在处理产品灰度...")):
                logger.info(f"处理进度 [{index + 1} / {total_pids}] - 产品ID: {pid}")
                
                try:
                    result = _panel_gray_operation(
                        panel_id=panel_id,
                        gray_type=gray_type,
                        user_list=user_list,
                        pid=pid,
                        env=env,
                        is_dry_run=is_dry_run
                    )
                    
                    results.append({
                        'panel_name': name,
                        'panel_id': panel_id,
                        'product_id': pid,
                        'operation': gray_type,
                        'success': result
                    })
                    
                    logger.info(f"产品 [{pid}] 灰度操作结果: [{result}]")
                    
                except Exception as e:
                    logger.error(f"处理产品 {pid} 时出错: {e}")
                    results.append({
                        'panel_name': name,
                        'panel_id': panel_id,
                        'product_id': pid,
                        'operation': gray_type,
                        'success': False,
                        'error': str(e)
                    })
        else:
            # 对整个面板进行灰度操作
            try:
                result = _panel_gray_operation(
                    panel_id=panel_id,
                    gray_type=gray_type,
                    user_list=user_list,
                    pid=None,
                    env=env,
                    is_dry_run=is_dry_run
                )
                
                results.append({
                    'panel_name': name,
                    'panel_id': panel_id,
                    'product_id': 'ALL',
                    'operation': gray_type,
                    'success': result
                })
                
                logger.info(f"面板 [{name}] 灰度操作结果: [{result}]")
                
            except Exception as e:
                logger.error(f"处理面板 {name} 时出错: {e}")
                results.append({
                    'panel_name': name,
                    'panel_id': panel_id,
                    'product_id': 'ALL',
                    'operation': gray_type,
                    'success': False,
                    'error': str(e)
                })

        # 输出结果
        if output_format != "text":
            format_output(results, output_format, title=f"面板灰度操作结果 ({env.upper()})")

        # 统计结果
        success_count = sum(1 for r in results if r['success'])
        total_count = len(results)
        
        if success_count == total_count:
            logger.info(f"所有操作均成功完成 ({success_count}/{total_count})")
        else:
            logger.warning(f"部分操作失败 ({success_count}/{total_count})")
            failed_items = [r for r in results if not r['success']]
            for item in failed_items:
                logger.warning(f"失败项: {item.get('product_id', 'ALL')} - {item.get('error', '未知错误')}")

    except CLIError as e:
        logger.error(f"面板灰度操作时出错: {e}")
        ctx.exit(1)
    except Exception as e:
        logger.error(f"发生意外错误: {e}", exc_info=True)
        ctx.exit(1)

    logger.info("面板灰度操作完成。")


def _panel_gray_operation(
    panel_id: str,
    gray_type: str,
    user_list: List[str],
    pid: Optional[str],
    env: str,
    is_dry_run: bool
) -> bool:
    """
    执行面板灰度操作。

    Args:
        panel_id: 面板ID
        gray_type: 灰度类型 ('Gray' 或 'Release')
        user_list: 用户列表
        pid: 产品ID (可选)
        env: 环境
        is_dry_run: 是否为空运行

    Returns:
        操作是否成功
    """
    if is_dry_run:
        logger.info(f"空运行模式：模拟灰度操作 (面板: {panel_id}, 产品: {pid or 'ALL'}, 类型: {gray_type})")
        if gray_type == app_config.GRAY_TYPE and user_list:
            logger.info(f"模拟灰度账号列表: {user_list}")
        return True

    try:
        # 构建灰度参数
        gated_param = {
            "panelId": panel_id,
            "isGated": gray_type == app_config.GRAY_TYPE,
        }
        
        # 如果是设置灰度，添加账号列表
        if gray_type == app_config.GRAY_TYPE:
            gated_param["accounts"] = user_list
        
        # 如果指定了产品ID，添加到参数中
        if pid:
            gated_param["pid"] = pid

        # 调用API
        result = ezviz_api.update_gray_info(gated_param, env=env)
        
        if result and gray_type == app_config.GRAY_TYPE and user_list:
            logger.info(f"灰度账号列表: {user_list}")
        
        return result
        
    except Exception as e:
        logger.error(f"执行灰度操作时出错: {e}")
        return False
