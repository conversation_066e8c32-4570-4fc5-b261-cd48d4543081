#!/bin/bash
# Linux/macOS脚本 - 为Ledvance CLI设置shell自动补全

echo "🚀 Ledvance CLI Shell自动补全设置"
echo "========================================"

# 检查ledvance是否已安装
if ! command -v ledvance &> /dev/null; then
    echo "❌ 错误: 未找到ledvance CLI工具"
    echo "请先安装ledvance CLI:"
    echo "  pip install -e ."
    exit 1
fi

echo "✅ 检测到ledvance CLI已安装"

# 检测当前shell
if [ -n "$ZSH_VERSION" ]; then
    CURRENT_SHELL="zsh"
elif [ -n "$BASH_VERSION" ]; then
    CURRENT_SHELL="bash"
elif [ -n "$FISH_VERSION" ]; then
    CURRENT_SHELL="fish"
else
    # 尝试从SHELL环境变量检测
    case "$SHELL" in
        */zsh)
            CURRENT_SHELL="zsh"
            ;;
        */bash)
            CURRENT_SHELL="bash"
            ;;
        */fish)
            CURRENT_SHELL="fish"
            ;;
        *)
            CURRENT_SHELL="bash"  # 默认
            ;;
    esac
fi

echo "🔍 检测到shell: $CURRENT_SHELL"

# 询问用户确认
read -p "是否为${CURRENT_SHELL}安装自动补全? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "📦 正在为${CURRENT_SHELL}安装自动补全..."
    
    if ledvance completion "$CURRENT_SHELL" --install; then
        echo
        echo "🎉 ${CURRENT_SHELL}自动补全安装成功！"
        echo
        echo "💡 使用方法:"
        
        case "$CURRENT_SHELL" in
            bash)
                echo "  1. 运行: source ~/.bashrc"
                echo "  2. 或重新启动终端"
                ;;
            zsh)
                echo "  1. 运行: source ~/.zshrc"
                echo "  2. 或重新启动终端"
                ;;
            fish)
                echo "  1. Fish shell的补全会自动生效"
                echo "  2. 无需额外操作"
                ;;
        esac
        
        echo
        echo "🔧 补全功能:"
        echo "  ledvance <TAB>          # 显示所有可用命令"
        echo "  ledvance tuya-<TAB>     # 显示tuya相关命令"
        echo "  ledvance --<TAB>        # 显示全局选项"
        echo
        
        # 提供快速测试方法
        if [ "$CURRENT_SHELL" != "fish" ]; then
            echo "🧪 快速测试 (在新终端中):"
            echo "  ledvance <TAB><TAB>"
        fi
        
    else
        echo "❌ 自动补全安装失败"
        echo "请手动运行: ledvance completion $CURRENT_SHELL --install"
        exit 1
    fi
else
    echo "取消安装"
    echo
    echo "手动安装方法:"
    echo "  ledvance completion $CURRENT_SHELL --install"
fi
