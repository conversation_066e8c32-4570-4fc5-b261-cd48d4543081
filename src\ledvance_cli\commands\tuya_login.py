"""
使用 Selenium 自动登录涂鸦平台并获取 Cookies 的命令。
"""
import json
import logging
import os
import time

import click
from selenium import webdriver
from selenium.common.exceptions import WebDriverException
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By

from ledvance_cli import config as app_config
from ledvance_cli.core.exceptions import CLIError
from ledvance_cli.utils import get_tuya_cookies_path

logger = logging.getLogger(__name__)


def cookies_to_dict(_cookies):
    """将 cookie 列表转换为字典。"""
    return {c['name']: c['value'] for c in _cookies}


@click.command("tuya-login")
@click.option(
    "--output-file",
    default="tuya-cookies.json",
    help="保存 cookies 的输出文件名。",
    type=click.Path(dir_okay=False, writable=True),
)
@click.pass_context
def tuya_login(ctx: click.Context, output_file: str) -> None:
    """
    启动一个浏览器会话以登录涂鸦 IoT 平台并保存凭证 cookies。

    此命令会自动填充 .env 文件中定义的 TUYA_USERNAME 和 TUYA_PASSWORD，
    但需要用户手动完成滑块验证。
    """
    is_dry_run = ctx.obj.dry_run

    logger.info("开始涂鸦平台登录流程...")
    if is_dry_run:
        logger.info("🔍 [DRY-RUN] 运行在模拟模式")

    # 读取配置
    tuya_username = app_config.APP_CONFIG.get("tuya_username")
    tuya_password = app_config.APP_CONFIG.get("tuya_password")

    if not tuya_username or not tuya_password:
        if is_dry_run:
            logger.info("🔍 [DRY-RUN] 使用模拟登录凭据")
            tuya_username = "<EMAIL>"
            tuya_password = "mock_password_123"
        else:
            raise CLIError(
                "错误：涂鸦用户名或密码未配置。\n"
                "请在您的 .env 文件中设置 TUYA_USERNAME 和 TUYA_PASSWORD。"
            )

    # 配置和启动WebDriver
    if is_dry_run:
        logger.info("🔍 [DRY-RUN] 模拟启动Chrome浏览器")
        driver = None
    else:
        logger.info("正在配置 Chrome WebDriver...")
        chrome_options = Options()
        chrome_options.add_argument('--log-level=3')  # 只显示严重错误
        prefs = {"intl.accept_languages": "en,en_US"}
        chrome_options.add_experimental_option("prefs", prefs)
        chrome_options.add_argument("--lang=en-US")

        try:
            driver = webdriver.Chrome(options=chrome_options)
        except WebDriverException as e:
            logger.error(f"WebDriver 启动失败: {e}")
            logger.error("请确保您已安装 Chrome 浏览器并且 chromedriver 在您的系统 PATH 中。")
            raise CLIError("WebDriver 初始化失败。") from e

    try:
        if is_dry_run:
            logger.info("🔍 [DRY-RUN] 模拟浏览器登录流程")
            # 创建模拟的cookies数据
            mock_cookies = [
                {"name": "sessionId", "value": "mock_session_12345"},
                {"name": "userId", "value": "mock_user_67890"},
                {"name": "csrf-token", "value": "mock_csrf_token_abcdef"},
                {"name": "loginTime", "value": str(int(time.time()))}
            ]
            cookie_dict = cookies_to_dict(mock_cookies)
        else:
            logger.info("正在打开涂鸦 IoT 平台登录页面...")
            driver.get('https://platform.tuya.com/pmg/list')
            time.sleep(1)

            logger.info("正在自动填充用户名和密码...")
            driver.find_element(By.ID, 'username').clear()
            driver.find_element(By.ID, 'username').send_keys(tuya_username)
            driver.find_element(By.ID, 'passwd').clear()
            driver.find_element(By.ID, 'passwd').send_keys(tuya_password)

            # 点击登录按钮
            driver.find_element(By.XPATH, '//button[@type="submit" and .//span[text()="Log in"]]').click()

            logger.info("浏览器已打开。请在新窗口中手动完成登录和滑块验证。")
            input("完成后，请按 Enter 键继续...")

            logger.info("登录完成，正在获取 cookies...")
            cookies = driver.get_cookies()
            cookie_dict = cookies_to_dict(cookies)

        # 保存 cookies
        cookies_path = get_tuya_cookies_path(output_file)

        if is_dry_run:
            logger.info(f"🔍 [DRY-RUN] 模拟保存cookies到: {cookies_path}")
        else:
            with open(cookies_path, 'w', encoding='utf-8') as f:
                json.dump(cookie_dict, f, ensure_ascii=False, indent=2)

        logger.info(f"Cookies 已成功保存到: {cookies_path}")

    except Exception as e:
        if is_dry_run:
            logger.info(f"🔍 [DRY-RUN] 模拟异常: {e}")
        else:
            logger.error(f"登录过程中出现错误: {e}")
            raise CLIError("获取 cookie 失败。")
    finally:
        if not is_dry_run and driver:
            logger.info("正在关闭浏览器...")
            driver.quit()