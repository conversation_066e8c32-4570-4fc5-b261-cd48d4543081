#!/usr/bin/env python3
"""
Ledvance CLI 自动补全功能演示脚本

这个脚本会展示各种自动补全的使用场景和效果。
"""

import subprocess
import sys
import time


def print_header(title):
    """打印标题"""
    print(f"\n{'='*60}")
    print(f"🎯 {title}")
    print('='*60)


def print_demo(description, command):
    """打印演示信息"""
    print(f"\n💡 {description}")
    print(f"📝 命令: {command}")
    print("🔄 输出:")
    print("-" * 40)


def run_completion_demo(shell='powershell'):
    """运行补全演示"""
    
    print("🚀 Ledvance CLI 自动补全功能演示")
    print("=" * 60)
    print(f"🖥️  演示环境: {shell}")
    
    # 1. 显示所有可用命令
    print_header("1. 命令名称补全")
    print_demo(
        "当您输入 'ledvance ' 并按Tab键时，会显示所有可用命令",
        "ledvance <TAB>"
    )
    
    try:
        result = subprocess.run(['ledvance', '--help'], 
                              capture_output=True, text=True, check=True)
        # 提取命令列表
        lines = result.stdout.split('\n')
        in_commands = False
        commands = []
        
        for line in lines:
            if 'Commands:' in line:
                in_commands = True
                continue
            elif in_commands and line.strip():
                if line.startswith('  ') and not line.startswith('    '):
                    cmd = line.strip().split()[0]
                    commands.append(cmd)
        
        print("可用命令:")
        for cmd in commands:
            print(f"  • {cmd}")
            
    except Exception as e:
        print(f"❌ 无法获取命令列表: {e}")
    
    # 2. 部分命令补全
    print_header("2. 部分命令补全")
    print_demo(
        "当您输入 'ledvance tuya-' 并按Tab键时，会显示所有tuya相关命令",
        "ledvance tuya-<TAB>"
    )
    
    tuya_commands = [cmd for cmd in commands if cmd.startswith('tuya-')]
    print("Tuya相关命令:")
    for cmd in tuya_commands:
        print(f"  • {cmd}")
    
    # 3. 选项补全
    print_header("3. 全局选项补全")
    print_demo(
        "当您输入 'ledvance --' 并按Tab键时，会显示所有全局选项",
        "ledvance --<TAB>"
    )
    
    global_options = [
        '--log-level', '--output', '--dry-run', 
        '--verbose', '--version', '--help'
    ]
    print("全局选项:")
    for opt in global_options:
        print(f"  • {opt}")
    
    # 4. 子命令选项补全
    print_header("4. 子命令选项补全")
    print_demo(
        "当您输入 'ledvance tuya-sync-product --' 并按Tab键时，会显示该命令的选项",
        "ledvance tuya-sync-product --<TAB>"
    )
    
    sync_options = [
        '--env', '--main-category', '--category', 
        '--rn-name', '--remark', '--help'
    ]
    print("tuya-sync-product 选项:")
    for opt in sync_options:
        print(f"  • {opt}")
    
    # 5. 参数值补全
    print_header("5. 参数值补全")
    print_demo(
        "当您输入 'ledvance --log-level ' 并按Tab键时，会显示可选的日志级别",
        "ledvance --log-level <TAB>"
    )
    
    log_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
    print("日志级别选项:")
    for level in log_levels:
        print(f"  • {level}")
    
    print_demo(
        "当您输入 'ledvance --output ' 并按Tab键时，会显示可选的输出格式",
        "ledvance --output <TAB>"
    )
    
    output_formats = ['text', 'json', 'yaml']
    print("输出格式选项:")
    for fmt in output_formats:
        print(f"  • {fmt}")
    
    # 6. 补全命令本身
    print_header("6. 补全命令管理")
    print_demo(
        "管理自动补全功能的命令",
        "ledvance completion <TAB>"
    )
    
    completion_options = ['bash', 'zsh', 'fish', 'powershell', '--install', '--help']
    print("补全管理选项:")
    for opt in completion_options:
        print(f"  • {opt}")


def show_installation_guide():
    """显示安装指南"""
    print_header("安装自动补全")
    
    print("🔧 快速安装方法:")
    print()
    print("1️⃣ 自动安装 (推荐):")
    print("   python scripts/install-completion.py")
    print()
    print("2️⃣ 手动安装:")
    print("   # Bash")
    print("   ledvance completion bash --install")
    print()
    print("   # Zsh")
    print("   ledvance completion zsh --install")
    print()
    print("   # Fish")
    print("   ledvance completion fish --install")
    print()
    print("   # PowerShell")
    print("   ledvance completion powershell --install")
    print()
    print("3️⃣ 平台特定脚本:")
    print("   # Windows")
    print("   scripts/setup-completion.bat")
    print()
    print("   # Linux/macOS")
    print("   scripts/setup-completion.sh")


def main():
    """主函数"""
    
    # 检查ledvance是否可用
    try:
        subprocess.run(['ledvance', '--version'], 
                      capture_output=True, check=True)
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ 错误: 未找到ledvance CLI工具")
        print("请先安装: pip install -e .")
        sys.exit(1)
    
    # 运行演示
    run_completion_demo()
    
    # 显示安装指南
    show_installation_guide()
    
    print_header("总结")
    print("🎉 Ledvance CLI 提供了强大的自动补全功能!")
    print()
    print("✨ 主要特性:")
    print("  • 智能命令名称补全")
    print("  • 上下文感知的选项补全")
    print("  • 参数值智能提示")
    print("  • 支持多种shell (Bash, Zsh, Fish, PowerShell)")
    print("  • 一键安装和配置")
    print()
    print("🚀 立即体验:")
    print("  1. 运行安装脚本启用补全")
    print("  2. 重新启动终端")
    print("  3. 输入 'ledvance ' 并按Tab键")
    print()
    print("📚 更多信息: docs/shell-completion.md")


if __name__ == "__main__":
    main()
