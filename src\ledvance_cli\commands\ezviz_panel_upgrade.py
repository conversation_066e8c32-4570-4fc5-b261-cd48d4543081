"""
Ezviz 面板升级命令 - 完整的面板升级流程。
"""
import json
import logging
from pathlib import Path
from typing import Dict, List, Optional

import click

from ledvance_cli import config as app_config
from ledvance_cli.commands.ezviz_panel_check import ezviz_panel_check
from ledvance_cli.core import ezviz_api
from ledvance_cli.core.exceptions import CLIError
from ledvance_cli.utils import format_output, increment_version, read_md5, show_progress

logger = logging.getLogger(__name__)


@click.command("ezviz-panel-upgrade")
@click.option(
    "--env",
    default="Test",
    type=click.Choice(['Prod', 'Test'], case_sensitive=True),
    help="要操作的平台环境：'Prod' (生产) 或 'Test' (测试)。"
)
@click.option(
    "--type",
    "operation_type",
    default="Upgrade",
    type=click.Choice(['Upgrade', 'Gray', 'Release'], case_sensitive=True),
    help="操作类型：'Upgrade' (升级), 'Gray' (灰度) 或 'Release' (发布)。"
)
@click.option(
    "--dir",
    "base_dir",
    type=click.Path(exists=True, path_type=Path),
    help="包所在目录。如果未指定，使用配置中的 base_dir。"
)
@click.option(
    "--version",
    help="面板版本。如果未指定，将自动递增当前版本。"
)
@click.option(
    "--app-version",
    help="最低 app 版本。"
)
@click.option(
    "--users",
    help="灰度账号列表，用逗号分隔。如果未指定，使用配置中的默认账号。"
)
@click.option(
    "--modules",
    help="模块名称列表，用逗号分隔。如果未指定，使用配置中的默认模块。"
)
@click.pass_context
def ezviz_panel_upgrade(
    ctx: click.Context,
    env: str,
    operation_type: str,
    base_dir: Optional[Path],
    version: Optional[str],
    app_version: Optional[str],
    users: Optional[str],
    modules: Optional[str]
):
    """
    Ezviz 面板升级命令 - 完整的面板升级流程。

    此命令支持完整的面板升级流程，包括：
    1. 升级面板状态
    2. 上传文件和更新面板信息
    3. 设置灰度用户
    4. 发布面板
    """
    output_format = ctx.obj.output_format
    is_dry_run = ctx.obj.dry_run

    # 验证操作类型
    valid_types = ['Upgrade', 'Gray', 'Release']
    if operation_type not in valid_types:
        raise CLIError(f"非法的操作类型: {operation_type}，支持的类型: {valid_types}")

    if is_dry_run:
        logger.info("🔍 [DRY-RUN] Ezviz面板升级操作预览")
        logger.info(f"🌐 目标环境: {env}")
        logger.info(f"🔧 操作类型: {operation_type}")
        logger.info(f"📁 基础目录: {base_dir if base_dir else '使用配置默认值'}")
        logger.info(f"📦 指定版本: {version if version else '自动递增'}")
        logger.info(f"📱 App版本: {app_version if app_version else '使用配置默认值'}")
        logger.info(f"👥 灰度用户: {users if users else '使用配置默认值'}")
        logger.info(f"📦 模块列表: {modules if modules else '使用配置默认值'}")
        logger.info("🔄 将要执行的操作:")
        logger.info("  1. 解析模块和用户列表")
        logger.info("  2. 获取面板信息和版本")
        logger.info("  3. 升级面板状态")
        logger.info("  4. 上传文件和更新面板信息")
        logger.info("  5. 设置灰度用户")
        logger.info("  6. 发布面板")
        logger.info("💡 注意: 在dry-run模式下，不会进行实际的API调用和文件操作")
        return

    logger.info(f"开始面板升级操作 (环境: {env}, 类型: {operation_type})")

    try:
        # 获取基础目录
        if not base_dir:
            base_dir = Path(app_config.APP_CONFIG.get("base_dir", "."))
        logger.info(f"使用基础目录: {base_dir}")

        # 解析用户列表
        user_list = []
        if users:
            user_list = [user.strip() for user in users.split(',') if user.strip()]
            logger.info(f"指定的灰度用户列表: {user_list}")
        else:
            user_list = app_config.APP_CONFIG.get("gray_accounts", [])
            if user_list:
                logger.info(f"使用配置中的默认灰度用户列表: {user_list}")

        # 解析模块列表
        module_list = []
        if modules:
            module_list = [module.strip() for module in modules.split(',') if module.strip()]
            logger.info(f"指定的模块列表: {module_list}")
        else:
            module_list = app_config.APP_CONFIG.get("default_device_modules", [])
            if module_list:
                logger.info(f"使用配置中的默认模块列表: {module_list}")
            else:
                raise CLIError("未指定模块列表，请使用 --modules 参数或在配置文件中设置 default_device_modules")

        # 获取模块映射
        module_map = app_config.APP_CONFIG.get("device_module_map", {})

        # 处理每个模块
        panel_versions = {}
        fail_list = []
        results = []

        total_modules = len(module_list)
        for index, module in enumerate(show_progress(module_list, description="正在处理面板升级...")):
            logger.info(f"处理进度 [{index + 1} / {total_modules}] - 模块: {module}")

            try:
                result = _deal_with_panel_upgrade(
                    module=module,
                    operation_type=operation_type,
                    env=env,
                    base_dir=base_dir,
                    version=version,
                    app_version=app_version,
                    user_list=user_list,
                    module_map=module_map,
                    panel_versions=panel_versions,
                    is_dry_run=is_dry_run
                )

                new_version = panel_versions.get(module, "未找到")
                logger.info(f"新版本: [{new_version}]")

                results.append({
                    'module': module,
                    'operation': operation_type,
                    'success': result,
                    'new_version': new_version
                })

                if not result:
                    fail_list.append(module)

            except Exception as e:
                logger.error(f"处理模块 {module} 时出错: {e}")
                fail_list.append(module)
                results.append({
                    'module': module,
                    'operation': operation_type,
                    'success': False,
                    'error': str(e)
                })

        # 输出结果
        if output_format != "text":
            format_output(results, output_format, title=f"面板升级操作结果 ({env.upper()})")

        # 统计结果
        success_count = len(module_list) - len(fail_list)
        if fail_list:
            logger.warning(f"失败列表: {fail_list}")

        if success_count == total_modules:
            logger.info(f"所有模块均成功完成 ({success_count}/{total_modules})")
        else:
            logger.warning(f"部分模块失败 ({success_count}/{total_modules})")

        # 执行版本检查（如果有成功的模块）
        if success_count > 0:
            _run_panel_check(env, operation_type, app_version)

    except CLIError as e:
        logger.error(f"面板升级操作时出错: {e}")
        ctx.exit(1)
    except Exception as e:
        logger.error(f"发生意外错误: {e}", exc_info=True)
        ctx.exit(1)

    logger.info("面板升级操作完成。")


def _deal_with_panel_upgrade(
    module: str,
    operation_type: str,
    env: str,
    base_dir: Path,
    version: Optional[str],
    app_version: Optional[str],
    user_list: List[str],
    module_map: Dict[str, str],
    panel_versions: Dict[str, str],
    is_dry_run: bool
) -> bool:
    """
    处理单个面板的升级流程。

    Args:
        module: 模块名称
        operation_type: 操作类型
        env: 环境键
        base_dir: 基础目录
        version: 指定版本
        app_version: App 版本
        user_list: 用户列表
        module_map: 模块映射
        panel_versions: 面板版本字典
        is_dry_run: 是否为空运行

    Returns:
        操作是否成功
    """
    try:
        # 获取面板信息
        if is_dry_run:
            logger.info(f"空运行模式：模拟查找面板 {module}")
            panel_list = [{
                'panelId': f'mock_panel_id_{module}',
                'rnName': module,
                'version': '1.0.0',
                'status': 'RELEASED'
            }]
        else:
            panel_list = ezviz_api.list_panel_by_name(module, env=env)

        if not panel_list:
            logger.error(f"面板 {module} 未找到")
            return False

        panel = panel_list[0]
        panel_versions[module] = panel['version']

        # 根据面板状态决定是否需要升级
        if panel['status'] == app_config.STATUS_RELEASED:
            upgraded = _upgrade_panel(panel, operation_type, env, is_dry_run)
        elif panel['status'] == app_config.STATUS_GRAYSCALE:
            logger.info(f"面板 [{module}] 处于灰度状态")
            upgraded = True
        else:
            logger.info(f"面板 [{module}] 已经升级")
            upgraded = True

        if upgraded:
            # 上传文件和更新面板信息
            can_next = _upload_and_update(
                panel, module, operation_type, env, base_dir,
                version, app_version, module_map, panel_versions, is_dry_run
            )
            if can_next:
                # 更新灰度用户
                can_next = _update_gray_user(panel, operation_type, user_list, env, is_dry_run)
            if can_next:
                # 发布面板
                return _release_panel(panel, operation_type, env, is_dry_run)

        return False

    except Exception as e:
        logger.error(f"处理面板 {module} 升级时出错: {e}")
        return False


def _upgrade_panel(panel: Dict, operation_type: str, env: str, is_dry_run: bool) -> bool:
    """升级面板状态。"""

    if operation_type in [app_config.GRAY_TYPE, app_config.RELEASE_TYPE]:
        logger.info(f"跳过升级面板 [{panel['rnName']}]")
        return True

    if is_dry_run:
        logger.info(f"空运行模式：模拟升级面板 {panel['rnName']}")
        return True

    # 设置新版本状态
    panel['status'] = app_config.STATUS_NEW_VERSION

    try:
        result = ezviz_api.upgrade_panel(panel, env=env)
        logger.info(f"面板 [{panel['rnName']}] 升级结果: [{result}]")
        return result
    except Exception as e:
        logger.error(f"升级面板时出错: {e}")
        return False


def _upload_and_update(
    panel: Dict,
    module: str,
    operation_type: str,
    env: str,
    base_dir: Path,
    version: Optional[str],
    app_version: Optional[str],
    module_map: Dict[str, str],
    panel_versions: Dict[str, str],
    is_dry_run: bool
) -> bool:
    """上传文件和更新面板信息。"""

    if operation_type in [app_config.GRAY_TYPE, app_config.RELEASE_TYPE]:
        logger.info(f"跳过上传和更新面板 [{module}]")
        return True

    if is_dry_run:
        logger.info(f"空运行模式：模拟上传和更新面板 {module}")
        # 模拟版本更新
        new_version = version if version else increment_version(panel['version'])
        panel_versions[panel['rnName']] = new_version
        return True

    try:
        # 获取 RN 模块名称
        rn_module = module_map.get(module, module)

        # 上传文件
        files = [f'cloud/{rn_module}-android.zip', f'cloud/{rn_module}-ios.zip']
        configs = []

        for file_name in files:
            file_path = base_dir / file_name
            if not file_path.exists():
                logger.error(f"文件不存在: {file_path}")
                return False

            config = ezviz_api.upload_file(str(file_name), env=env)
            if not config:
                logger.error(f"上传文件 {file_name} 失败")
                return False
            configs.append(config)

        # 更新面板版本和配置
        new_version = version if version else increment_version(panel['version'])
        panel['version'] = new_version

        if app_version:
            panel['appVersion'] = app_version

        # 设置 Android 和 iOS 配置
        panel['androidConfig'], panel['iosConfig'] = configs

        # 更新 EIB 配置
        eib_config = json.loads(panel.get('eibConfig', '{}'))
        eib_config['androidMd5'] = read_md5(f'{base_dir}/cloud/{rn_module}-android.md5')
        eib_config['iosMd5'] = read_md5(f'{base_dir}/cloud/{rn_module}-ios.md5')
        panel['eibConfig'] = json.dumps(eib_config)

        panel_versions[panel['rnName']] = panel['version']

        # 更新面板信息
        return _update_panel_info(panel, operation_type, env, is_dry_run)

    except Exception as e:
        logger.error(f"上传和更新面板时出错: {e}")
        return False


def _update_panel_info(panel: Dict, operation_type: str, env: str, is_dry_run: bool) -> bool:
    """更新面板信息。"""

    if operation_type in [app_config.GRAY_TYPE, app_config.RELEASE_TYPE]:
        logger.info(f"跳过更新面板 [{panel['rnName']}]")
        return True

    if is_dry_run:
        logger.info(f"空运行模式：模拟更新面板信息 {panel['rnName']}")
        return True

    try:
        result = ezviz_api.update_panel(panel, env=env)
        logger.info(f"更新面板 [{panel['rnName']}] 信息结果: [{result}]")
        return result
    except Exception as e:
        logger.error(f"更新面板信息时出错: {e}")
        return False


def _update_gray_user(panel: Dict, operation_type: str, user_list: List[str], env: str, is_dry_run: bool) -> bool:
    """更新灰度用户。"""

    if operation_type == app_config.RELEASE_TYPE:
        logger.info(f"跳过更新面板 [{panel['rnName']}] 的灰度用户")
        return True

    if is_dry_run:
        logger.info(f"空运行模式：模拟更新面板 {panel['rnName']} 的灰度用户")
        if user_list:
            logger.info(f"模拟灰度账号列表: {user_list}")
        return True

    try:
        gated_param = {
            "panelId": panel['panelId'],
            "isGated": True,
            "accounts": user_list
        }

        result = ezviz_api.update_gray_info(gated_param, env=env)
        logger.info(f"更新面板 [{panel['rnName']}] 灰度用户结果: [{result}]")

        if result and user_list:
            logger.info(f"灰度账号列表: {user_list}")

        return result
    except Exception as e:
        logger.error(f"更新灰度用户时出错: {e}")
        return False


def _release_panel(panel: Dict, operation_type: str, env: str, is_dry_run: bool) -> bool:
    """发布面板。"""

    if panel['status'] == app_config.STATUS_RELEASED:
        logger.info(f"面板 [{panel['rnName']}] 已经发布")
        return True

    if operation_type != app_config.RELEASE_TYPE:
        logger.info(f"跳过发布面板 [{panel['rnName']}]")
        return True

    if is_dry_run:
        logger.info(f"空运行模式：模拟发布面板 {panel['rnName']}")
        return True

    try:
        gated_param = {
            "panelId": panel['panelId'],
            "isGated": False
        }

        result = ezviz_api.update_gray_info(gated_param, env=env)
        logger.info(f"发布面板 [{panel['rnName']}] 结果: [{result}]")
        return result
    except Exception as e:
        logger.error(f"发布面板时出错: {e}")
        return False


def _run_panel_check(env: str, operation_type: str, app_version: Optional[str]) -> None:
    """
    运行面板版本检查。

    使用 ctx.invoke 方式调用 ezviz-panel-check 命令。
    """
    try:
        check_type = "Release" if operation_type == "Release" else "Gray"

        # 使用 ctx.invoke 方式调用 ezviz-panel-check 命令
        try:
            ctx = click.get_current_context()
            logger.debug(f"准备调用 ezviz-panel-check: env={env.title()}, type={check_type}, app_version={app_version}")

            ctx.invoke(
                ezviz_panel_check,
                env=env.title(),
                check_type=check_type,
                app_version=app_version,
            )

        except Exception as invoke_error:
            logger.error(f"调用 ezviz-panel-check 时出错: {invoke_error}")
            # 不抛出异常，因为这不应该阻止主流程

    except Exception as e:
        logger.error(f"运行面板版本检查时出错: {e}")
