"""
Ezviz API 客户端模块。

封装了与 Ezviz 平台交互的逻辑，支持平台多环境会话管理和所有 API 调用。
"""
import hashlib
import json
import logging
import os
import random
import time
import urllib.parse
from typing import Dict, Optional

import requests

from ledvance_cli import config as app_config
from ledvance_cli.core.exceptions import CLIError
from ledvance_cli.utils import convert_to_dict, retry

logger = logging.getLogger(__name__)

# --- 全局会话缓存 ---
_platform_sessions: Dict[str, requests.Session] = {}
_app_session_id: Optional[str] = None


def _get_platform_config(env: str) -> Dict[str, str]:
    """根据环境标识获取对应的平台配置。"""
    env_key = env.lower()
    config_keys = {
        "host": f"ezviz_host_{env_key}",
        "account": f"ezviz_account_{env_key}",
        "password": f"ezviz_password_{env_key}",
    }
    config = {key: app_config.APP_CONFIG.get(value) for key, value in config_keys.items()}
    if not all(config.values()):
        raise CLIError(f"环境 '{env}' 的 Ezviz 平台配置不完整。请检查 .env 文件。")
    return config


def _res_success(response: requests.Response):
    """检查响应是否成功。"""
    if response.ok:
        try:
            data = response.json()
            code = data.get('code', data.get('meta', {}).get('code'))
            return str(code) == '200'
        except requests.JSONDecodeError:
            logger.warning("响应不是有效的 JSON 格式。")
            return False
    return False


# --- 平台会话管理 ---
@retry()
def _login_platform(env: str):
    """登录到指定环境的 Ezviz 平台并返回一个会话对象。"""
    config = _get_platform_config(env)
    md = hashlib.md5(config['password'].encode('utf-8')).hexdigest()
    payload = {'account': config['account'], 'indexcode': config['account'], 'password': md, 'smsCode': ''}
    login_url = f"{config['host']}/user/login"
    session = requests.Session()
    response = session.post(login_url, data=payload)
    if response.ok:
        logger.info(f"成功登录到 Ezviz 平台 (环境: {env})")
        return session
    else:
        raise CLIError(f"登录 Ezviz 平台 (环境: {env}) 失败: {response.text}")


def get_platform_session(env: str = 'prod') -> requests.Session:
    """获取一个指定环境的有效平台会话，如果不存在则自动登录。"""
    env_key = env.lower()
    if env_key not in _platform_sessions:
        logger.info(f"环境 '{env_key}' 的平台会话不存在，正在创建新会话...")
        _platform_sessions[env_key] = _login_platform(env_key)
    return _platform_sessions[env_key]


# --- App 会话管理 (单一环境) ---
@retry()
def _login_app(request_body: str):
    """登录到 Ezviz App 并返回一个 sessionId。"""
    app_host = app_config.APP_CONFIG.get("ezviz_app_host")
    if not app_host:
        raise CLIError("Ezviz App Host (ezviz_app_host) 未配置。")
    timestamp = int(time.time() * 1000)
    params = {k: v[0] for k, v in urllib.parse.parse_qs(request_body).items()}
    sorted_keys = sorted(params.keys())
    sign_params = ('hik88075998' + '&'.join(f'{key}={params[key]}' for key in sorted_keys) + f'&timestamp={timestamp}hik88075998').upper()
    sign = hashlib.md5(sign_params.encode('utf-8')).hexdigest()
    headers = {
        "Content-Type": "application/x-www-form-urlencoded", "appId": "ed7ed022c36943d59f14a2f4fef587e6",
        "clientType": "1", "clientVersion": "V1.5.0 2000321", "featureCode": "6e6edacd6ea7762fe7d92380c33ede00",
        "cname": "aVBob25lMTAsMg==", "sign": sign, "timestamp": str(timestamp)
    }
    response = requests.post(f"{app_host}/v3/users/ldws/login/v2", headers=headers, data=request_body)
    if _res_success(response):
        logger.info(f"成功登录到 Ezviz App")
        return response.json()['sessionInfo']['sessionId']
    else:
        raise CLIError(f"登录 Ezviz App 失败: {response.text}")


def get_app_session_id(request_body: str) -> str:
    """获取一个有效的 App sessionId，如果不存在则自动登录。"""
    global _app_session_id
    if _app_session_id is None:
        logger.info("App sessionId 不存在，正在创建新会话...")
        _app_session_id = _login_app(request_body)
    return _app_session_id


# --- API 函数 ---

@retry()
def list_panel_by_name(rn_name: str, env: str = 'prod'):
    session = get_platform_session(env)
    host = _get_platform_config(env)['host']
    url = f'{host}/iot/config/api/iot/support/v3/panel/gated/page'
    response = session.post(url, json={"pageStart": 0, "pageSize": 10, "firstCategory": "", "secondCategory": "", "rnName": rn_name, "panelName": rn_name, "allRnPanelStatus": True})
    if _res_success(response):
        return response.json()['data']
    else:
        logger.error(f'根据名称 [{rn_name}] 请求面板 (环境: {env}) 失败: {response.text}')
        return None

@retry()
def set_product_panel(panel_info: dict, env: str = 'prod'):
    session = get_platform_session(env)
    host = _get_platform_config(env)['host']
    url = f'{host}/iot/config/api/iot/support/v3/panel/pid/select'
    response = session.post(url, json=panel_info)
    if _res_success(response):
        return True
    else:
        logger.error(f'设置产品面板 (环境: {env}) 失败: {response.text}')
        return False

@retry()
def upgrade_panel(panel_info: dict, env: str = 'prod'):
    session = get_platform_session(env)
    host = _get_platform_config(env)['host']
    url = f'{host}/iot/config/api/iot/support/v3/panel/upgrade'
    response = session.put(url, json=panel_info)
    if _res_success(response):
        return True
    else:
        logger.error(f'升级面板 (环境: {env}) 失败: {response.text}')
        return False

@retry()
def upload_file(file_name: str, env: str = 'prod'):
    session = get_platform_session(env)
    host = _get_platform_config(env)['host']
    base_dir = app_config.APP_CONFIG.get("base_dir", ".")
    file_path = os.path.join(base_dir, file_name)
    if not os.path.exists(file_path):
        raise CLIError(f"文件不存在: {file_path}")
    with open(file_path, 'rb') as f:
        files = {'picFile': (file_name, f, 'application/x-zip-compressed')}
        url = f'{host}/iot/config/api/iot/support/panel/pic/up'
        response = session.post(url, files=files)
    if _res_success(response):
        logger.info(f"文件 [{file_name}] 上传 (环境: {env}) 成功。")
        return response.json()['data']['pic']
    else:
        logger.error(f"文件 [{file_name}] 上传 (环境: {env}) 失败: {response.text}")
        return None

@retry()
def update_panel(panel_info: dict, env: str = 'prod'):
    session = get_platform_session(env)
    host = _get_platform_config(env)['host']
    url = f'{host}/iot/config/api/iot/support/v3/panel/update'
    response = session.put(url, json=panel_info)
    if _res_success(response):
        return True
    else:
        logger.error(f'更新面板 (环境: {env}) 失败: {response.text}')
        return False

@retry()
def update_gray_info(gray_param: dict, env: str = 'prod'):
    session = get_platform_session(env)
    host = _get_platform_config(env)['host']
    url = f'{host}/iot/config/api/iot/support/v3/panel/gatedRelease'
    response = session.post(url, json=gray_param, timeout=(120, 120))
    if _res_success(response):
        return True
    else:
        logger.error(f'更新灰度信息 (环境: {env}) 失败: {response.text}')
        return False

@retry()
def get_product_panel_version(product_list: list[dict], request_body: str):
    """获取产品面板版本信息。"""
    app_host = app_config.APP_CONFIG.get("ezviz_app_host")
    if not app_host:
        raise CLIError("Ezviz App Host (ezviz_app_host) 未配置。")

    session_id = get_app_session_id(request_body)
    response = requests.post(
        f'{app_host}/v3/iot-feature/product/configs/appVersion',
        headers={'Content-Type': 'application/json', 'sessionId': session_id},
        json=product_list
    )
    if _res_success(response):
        return response.json()['data']
    else:
        logger.error(f'获取产品面板版本失败: {response.text}')
        return None


def login_app(request_body: str) -> str:
    """公开的 App 登录函数，返回 sessionId。"""
    return get_app_session_id(request_body)

@retry()
def page_product_list(query_param: dict, env: str = 'prod'):
    session = get_platform_session(env)
    host = _get_platform_config(env)['host']
    url = f'{host}/iot/config/api/iot/support/productV3/page'
    response = session.post(url, json=query_param)
    if _res_success(response):
        data = response.json()
        return data['data'], data['page']['total']
    else:
        logger.error(f'列出产品 (环境: {env}) 失败: {response.text}')
        return [], 0

def get_all_product(base_payload: dict, env: str = 'prod'):
    """
    获取所有产品，自动处理分页。
    """
    all_products = []
    current_page = 0
    page_size = 200  # 使用较大的页面大小以减少请求次数

    logger.info("正在从Ezviz平台获取所有产品，这可能需要一些时间...")

    while True:
        # 复制基础 payload 并更新分页参数
        payload = base_payload.copy()
        payload["pageStart"] = current_page
        payload["pageSize"] = page_size

        try:
            products, total = page_product_list(payload, env)
        except CLIError as e:
            logger.error(f"获取产品时出错: {e}")
            break

        if not products:
            logger.info("已获取所有产品。")
            break

        all_products.extend(products)
        total_pages = (total + page_size - 1) // page_size
        logger.info(f"已获取第 {current_page}/{total_pages} 页，累计 {len(all_products)}/{total} 个产品...")

        if current_page >= total_pages:
            break
        current_page += 1

    logger.info(f"数据获取完成，共 {len(all_products)} 条产品记录。")
    return all_products


@retry()
def list_product_panel(query_param: dict, env: str = 'prod'):
    session = get_platform_session(env)
    host = _get_platform_config(env)['host']
    url = f'{host}/iot/config/api/iot/support/v3/panel/withPid'
    response = session.get(url, params=query_param)
    if _res_success(response):
        return response.json()['data']
    else:
        logger.error(f'列出产品面板 (环境: {env}) 失败: {response.text}')
        return []

@retry()
def list_available_panel(query_param: dict, env: str = 'prod'):
    session = get_platform_session(env)
    host = _get_platform_config(env)['host']
    url = f'{host}/iot/config/api/iot/support/v3/panel/panelByPid'
    response = session.post(url, json=query_param)
    if _res_success(response):
        return response.json()['data']
    else:
        logger.error(f'列出可用面板 (环境: {env}) 失败: {response.text}')
        return []

@retry()
def publish_product(publish_param: dict, env: str = 'prod'):
    session = get_platform_session(env)
    host = _get_platform_config(env)['host']
    url = f'{host}/iot/config/api/iot/support/product/release'
    response = session.post(url, params=publish_param)
    if _res_success(response):
        return True
    else:
        logger.error(f'发布产品 (环境: {env}) 失败: {response.text}')
        return False

@retry()
def unpublish_product(pid: str, env: str = 'prod'):
    session = get_platform_session(env)
    host = _get_platform_config(env)['host']
    url = f'{host}/iot/config/api/iot/support/product/unPublish'
    response = session.post(url, params={'pid': pid})
    if _res_success(response):
        return True
    else:
        logger.error(f'取消发布产品 (环境: {env}) 失败: {response.text}')
        return False

@retry()
def delete_product(pid: str, env: str = 'prod'):
    session = get_platform_session(env)
    host = _get_platform_config(env)['host']
    url = f'{host}/iot/config/api/iot/support/product/delete'
    response = session.post(url, json={'pid': pid})
    if _res_success(response):
        return True
    else:
        logger.error(f'删除产品 (环境: {env}) 失败: {response.text}')
        return False

@retry()
def get_product_features(pid: str, env: str = 'prod'):
    session = get_platform_session(env)
    host = _get_platform_config(env)['host']
    url = f'{host}/iot/config/api/iot/support/product/feature/{pid}/page'
    response = session.post(url)
    if _res_success(response):
        return response.json()['data']
    else:
        logger.error(f'获取产品功能 (环境: {env}) 失败: {response.text}')
        return None

@retry()
def update_product_feature(payload: dict, env: str = 'prod'):
    session = get_platform_session(env)
    host = _get_platform_config(env)['host']
    url = f'{host}/iot/config/api/iot/support/product/feature/update'
    response = session.post(url, json=payload)
    if _res_success(response):
        return True
    else:
        logger.error(f'更新产品功能 (环境: {env}) 失败: {response.text}')
        return False

@retry()
def get_tag_list(env: str = 'prod'):
    session = get_platform_session(env)
    host = _get_platform_config(env)['host']
    url = f'{host}/iot/config/api/iot/support/v3/tag/page'
    response = session.post(url, json={'pageSize': 5000, 'pageStart': 0})
    if _res_success(response):
        return response.json()['data']
    else:
        logger.error(f'获取标签列表 (环境: {env}) 失败: {response.text}')
        return []

@retry()
def check_global_panel_name(rn_name: str, env: str = 'prod'):
    session = get_platform_session(env)
    host = _get_platform_config(env)['host']
    url = f'{host}/iot/config/api/iot/support/v3/global/panel/findByName'
    response = session.get(url, params={'rnName': rn_name, '_r': random.random})
    if _res_success(response):
        return response.json()['data']
    else:
        logger.error(f'检查全局面板名称 (环境: {env}) 失败: {response.text}')
        return True

@retry()
def create_global_panel(payload: dict, env: str = 'prod'):
    session = get_platform_session(env)
    host = _get_platform_config(env)['host']
    url = f'{host}/iot/config/api/iot/support/v3/global/panel/add'
    response = session.post(url, json=payload)
    if _res_success(response):
        return response.json()['data']
    else:
        logger.error(f'创建全局面板 (环境: {env}) 失败: {response.text}')
        return None

@retry()
def update_global_panel_gray_info(gray_param: dict, env: str = 'prod'):
    session = get_platform_session(env)
    host = _get_platform_config(env)['host']
    url = f'{host}/iot/config/api/iot/support/v3/global/panel/gatedRelease'
    response = session.post(url, json=gray_param, timeout=(120, 120))
    if _res_success(response):
        return True
    else:
        logger.error(f'更新全局面板灰度信息 (环境: {env}) 失败: {response.text}')
        return False

@retry()
def list_global_panel_by_name(rn_name: str, env: str = 'prod'):
    session = get_platform_session(env)
    host = _get_platform_config(env)['host']
    url = f'{host}/iot/config/api/iot/support/v3/global/panel/page'
    response = session.post(url, json={"pageStart": 0, "pageSize": 10, "panelName": rn_name, "rnName": rn_name, "allRnPanelStatus": True})
    if _res_success(response):
        return response.json()['data']
    else:
        logger.error(f'按名称列出全局面板 (环境: {env}) 失败: {response.text}')
        return None

@retry()
def upgrade_global_panel(panel_info: dict, env: str = 'prod'):
    session = get_platform_session(env)
    host = _get_platform_config(env)['host']
    url = f'{host}/iot/config/api/iot/support/v3/global/panel/upgrade'
    response = session.put(url, json=panel_info)
    if _res_success(response):
        return True
    else:
        logger.error(f'升级全局面板 (环境: {env}) 失败: {response.text}')
        return False

@retry()
def update_global_panel(panel_info: dict, env: str = 'prod'):
    session = get_platform_session(env)
    host = _get_platform_config(env)['host']
    url = f'{host}/iot/config/api/iot/support/v3/global/panel/update'
    response = session.put(url, json=panel_info)
    if _res_success(response):
        return True
    else:
        logger.error(f'更新全局面板 (环境: {env}) 失败: {response.text}')
        return False

@retry()
def get_category_map(env: str = 'prod'):
    session = get_platform_session(env)
    host = _get_platform_config(env)['host']
    url = f'{host}/iot/config/api/iot/support/product/category/list'
    response = session.get(url, params={'productLine': 'ys', 'offset': 0, 'limit': 1000, '_r': random.random()})
    if _res_success(response):
        return convert_to_dict(response.json()['data'])
    else:
        logger.error(f'获取品类映射 (环境: {env}) 失败: {response.text}')
        return None

@retry()
def get_template_features(category: str, env: str = 'prod'):
    session = get_platform_session(env)
    host = _get_platform_config(env)['host']
    url = f'{host}/iot/config/api/iot/support/product/feature/template/page'
    response = session.get(url, params={'category': category, '_r': random.random(), 'offset': 0, 'limit': 10000})
    if _res_success(response):
        return response.json()['data']
    else:
        logger.error(f'获取模板功能 (环境: {env}) 失败: {response.text}')
        return None

@retry()
def add_template_features_to_product(pid: str, features: list[dict], env: str = 'prod'):
    session = get_platform_session(env)
    host = _get_platform_config(env)['host']
    url = f'{host}/iot/config/api/iot/support/product/feature/{pid}/addPatch'
    response = session.post(url, json={'pId': pid, 'featureDtos': features})
    if _res_success(response):
        return True
    else:
        logger.error(f'向产品添加模板功能 (环境: {env}) 失败: {response.text}')
        return False

@retry()
def get_product_info(pid: str, env: str = 'prod'):
    session = get_platform_session(env)
    host = _get_platform_config(env)['host']
    url = f'{host}/iot/config/api/iot/support/product/info'
    response = session.get(url, params={'productId': pid, '_r': random.random()})
    if _res_success(response):
        return response.json()['data']
    else:
        logger.error(f'获取产品信息 (环境: {env}) 失败: {response.text}')
        return None

@retry()
def upload_picture(file_path: str, env: str = 'prod'):
    session = get_platform_session(env)
    host = _get_platform_config(env)['host']
    url = f'{host}/iot/consoleapi/product/upload'
    with open(file_path, 'rb') as f:
        files = {'file': (os.path.basename(file_path), f, 'application/x-zip-compressed')}
        response = session.post(url, files=files)
    if _res_success(response):
        return response.json()['data']['url']
    else:
        logger.error(f'上传图片 (环境: {env}) 失败: {response.text}')
        return None

@retry()
def check_rn_name(rn_name: str, env: str = 'prod'):
    """检查 RN 名称是否已存在。"""
    session = get_platform_session(env)
    host = _get_platform_config(env)['host']
    url = f'{host}/iot/config/api/iot/support/v3/panel/panelName/isReport'
    response = session.get(url, params={'rnName': rn_name, '_r': random.random()})
    if _res_success(response):
        return response.json()['data']
    else:
        logger.error(f'检查 RN 名称 (环境: {env}) 失败: {response.text}')
        return True

@retry()
def create_panel(payload: dict, env: str = 'prod'):
    """创建面板。"""
    session = get_platform_session(env)
    host = _get_platform_config(env)['host']
    url = f'{host}/iot/config/api/iot/support/v3/panel/add'
    response = session.post(url, json=payload)
    if _res_success(response):
        return response.json()['data']
    else:
        logger.error(f'创建面板 (环境: {env}) 失败: {response.text}')
        return None

@retry()
def create_product(payload: dict, env: str = 'prod'):
    session = get_platform_session(env)
    host = _get_platform_config(env)['host']
    url = f'{host}/iot/config/api/iot/support/product/create'
    response = session.post(url, params={'productId': payload['productId']}, json=payload)
    if _res_success(response):
        return True
    else:
        logger.error(f'创建产品 (环境: {env}) 失败: {response.text}')
        return False

@retry()
def delete_feature(payload: dict, env: str = 'prod'):
    session = get_platform_session(env)
    host = _get_platform_config(env)['host']
    url = f'{host}/iot/config/api/iot/support/product/feature/delete'
    response = session.post(url, json=payload)
    if _res_success(response):
        return True
    else:
        logger.error(f'删除功能 (环境: {env}) 失败: {response.text}')
        return False

@retry()
def get_resource_template(category: str, env: str = 'prod'):
    session = get_platform_session(env)
    host = _get_platform_config(env)['host']
    url = f'{host}/iot/config/api/iot/support/std/resourceTpl'
    response = session.get(url, params={'subCatKey': category, '_r': random.random()})
    if _res_success(response) and len(response.json()['data']) > 0:
        return response.json()['data'][0]
    else:
        logger.error(f'获取资源模板 (环境: {env}) 失败: {response.text}')
        return None

@retry()
def get_resource_template_features(resource_id: str, env: str = 'prod'):
    session = get_platform_session(env)
    host = _get_platform_config(env)['host']
    url = f'{host}/iot/config/api/iot/support/std/feature'
    response = session.get(url, params={'resourceId': resource_id, 'pageStart': 0, 'pageSize': 1000, '_r': random.random()})
    if _res_success(response):
        return response.json()['data']
    else:
        logger.error(f'获取资源模板功能 (环境: {env}) 失败: {response.text}')
        return []

@retry()
def add_template_feature(payload: dict, env: str = 'prod'):
    session = get_platform_session(env)
    host = _get_platform_config(env)['host']
    url = f'{host}/iot/config/api/iot/support/std/product/feature'
    response = session.post(url, json=payload)
    if _res_success(response):
        return True
    else:
        logger.error(f'添加模板功能 (环境: {env}) 失败: {response.text}')
        return False
