"""
外部脚本执行模块。
"""

import logging
import subprocess
from typing import <PERSON>ple

from ledvance_cli.core.exceptions import CommandExecutionError

logger = logging.getLogger(__name__)

def execute_script(
    command: str,
    capture_output: bool = True,
    text: bool = True,
    check: bool = True
) -> Tuple[str, str, int]:
    """
    执行外部命令或脚本。

    Args:
        command: 要执行的命令。
        capture_output: 是否捕获 stdout 和 stderr。
        text: 是否将 stdout/stderr 解码为文本。
        check: 如果为 True，则在非零退出代码时引发 CommandExecutionError。

    Returns:
        一个包含 (stdout, stderr, return_code) 的元组。

    Raises:
        CommandExecutionError: 如果命令返回非零退出代码且 check 为 True。
        FileNotFoundError: 如果未找到命令。
    """
    logger.info(f"正在执行外部命令: {command}")
    try:
        process = subprocess.run(
            command,
            shell=True,
            capture_output=capture_output,
            text=text,
            check=False  # 我们手动处理检查
        )

        if check and process.returncode != 0:
            raise CommandExecutionError(
                command=command,
                stdout=process.stdout,
                stderr=process.stderr,
                exit_code=process.returncode,
            )
        
        logger.debug(f"命令 '{command}' 以退出代码 {process.returncode} 完成")
        return process.stdout, process.stderr, process.returncode

    except FileNotFoundError as e:
        logger.error(f"未找到命令: {command.split()[0]}")
        raise e
    except Exception as e:
        logger.error(f"执行命令 '{command}' 时发生意外错误: {e}")
        raise
