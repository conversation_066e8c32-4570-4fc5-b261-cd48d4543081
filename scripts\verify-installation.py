#!/usr/bin/env python3
"""
Ledvance CLI 安装验证脚本

这个脚本会验证Ledvance CLI是否正确安装并可以正常工作。
"""

import subprocess
import sys
import tempfile
from pathlib import Path


def print_header(title):
    """打印标题"""
    print(f"\n{'='*60}")
    print(f"🔍 {title}")
    print('='*60)


def print_step(step, description):
    """打印步骤"""
    print(f"\n{step} {description}")
    print("-" * 40)


def check_command_availability():
    """检查命令是否可用"""
    print_step("1️⃣", "检查命令可用性")
    
    try:
        result = subprocess.run(['ledvance', '--version'], 
                              capture_output=True, text=True, check=True)
        version = result.stdout.strip()
        print(f"✅ ledvance命令可用: {version}")
        return True, version
    except (subprocess.CalledProcessError, FileNotFoundError) as e:
        print(f"❌ ledvance命令不可用: {e}")
        return False, None


def check_help_system():
    """检查帮助系统"""
    print_step("2️⃣", "检查帮助系统")
    
    try:
        # 检查主帮助
        result = subprocess.run(['ledvance', '--help'], 
                              capture_output=True, text=True, check=True)
        
        help_content = result.stdout
        
        # 检查关键内容
        checks = [
            ('Commands:', '包含命令列表'),
            ('Options:', '包含选项列表'),
            ('--help', '包含帮助选项'),
            ('--version', '包含版本选项'),
        ]
        
        for check, description in checks:
            if check in help_content:
                print(f"✅ {description}")
            else:
                print(f"❌ 缺少: {description}")
                return False
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 帮助系统检查失败: {e}")
        return False


def check_commands():
    """检查可用命令"""
    print_step("3️⃣", "检查可用命令")
    
    expected_commands = [
        'completion',
        'ezviz-panel-check',
        'ezviz-panel-upgrade',
        'tuya-dp-check',
        'tuya-login',
        'tuya-sync-product'
    ]
    
    try:
        result = subprocess.run(['ledvance', '--help'], 
                              capture_output=True, text=True, check=True)
        
        help_content = result.stdout
        
        # 提取命令列表
        lines = help_content.split('\n')
        found_commands = []
        in_commands = False
        
        for line in lines:
            if 'Commands:' in line:
                in_commands = True
                continue
            elif in_commands and line.strip():
                if line.startswith('  ') and not line.startswith('    '):
                    cmd = line.strip().split()[0]
                    found_commands.append(cmd)
        
        print(f"发现 {len(found_commands)} 个命令:")
        
        all_found = True
        for cmd in expected_commands:
            if cmd in found_commands:
                print(f"✅ {cmd}")
            else:
                print(f"❌ 缺少命令: {cmd}")
                all_found = False
        
        # 检查额外命令
        extra_commands = set(found_commands) - set(expected_commands)
        if extra_commands:
            print(f"\n额外命令: {', '.join(extra_commands)}")
        
        return all_found
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 命令检查失败: {e}")
        return False


def check_command_help():
    """检查各命令的帮助"""
    print_step("4️⃣", "检查命令帮助")
    
    commands_to_test = [
        'completion',
        'tuya-login',
        'ezviz-panel-check'
    ]
    
    all_passed = True
    
    for cmd in commands_to_test:
        try:
            result = subprocess.run(['ledvance', cmd, '--help'], 
                                  capture_output=True, text=True, check=True)
            
            if result.stdout.strip():
                print(f"✅ {cmd} --help 工作正常")
            else:
                print(f"❌ {cmd} --help 输出为空")
                all_passed = False
                
        except subprocess.CalledProcessError as e:
            print(f"❌ {cmd} --help 失败: {e}")
            all_passed = False
    
    return all_passed


def check_completion_feature():
    """检查自动补全功能"""
    print_step("5️⃣", "检查自动补全功能")
    
    try:
        # 检查completion命令
        result = subprocess.run(['ledvance', 'completion', '--help'], 
                              capture_output=True, text=True, check=True)
        
        help_content = result.stdout
        
        if 'bash|zsh|fish|powershell' in help_content:
            print("✅ 自动补全命令可用")
            
            # 测试生成补全脚本
            try:
                result = subprocess.run(['ledvance', 'completion', 'bash'], 
                                      capture_output=True, text=True, check=True)
                
                if result.stdout.strip():
                    print("✅ 补全脚本生成正常")
                    return True
                else:
                    print("❌ 补全脚本生成为空")
                    return False
                    
            except subprocess.CalledProcessError as e:
                print(f"❌ 补全脚本生成失败: {e}")
                return False
        else:
            print("❌ 自动补全功能不完整")
            return False
            
    except subprocess.CalledProcessError as e:
        print(f"❌ 自动补全功能检查失败: {e}")
        return False


def check_configuration():
    """检查配置系统"""
    print_step("6️⃣", "检查配置系统")
    
    try:
        # 运行一个简单的命令来触发配置加载
        result = subprocess.run(['ledvance', 'tuya-login', '--help'], 
                              capture_output=True, text=True, check=True)
        
        # 检查是否有配置相关的错误
        if 'error' not in result.stderr.lower() and 'traceback' not in result.stderr.lower():
            print("✅ 配置系统工作正常")
            return True
        else:
            print(f"❌ 配置系统可能有问题: {result.stderr}")
            return False
            
    except subprocess.CalledProcessError as e:
        print(f"❌ 配置系统检查失败: {e}")
        return False


def check_dependencies():
    """检查依赖包"""
    print_step("7️⃣", "检查依赖包")

    # 包名映射：显示名 -> 导入名
    required_packages = {
        'click': 'click',
        'rich': 'rich',
        'PyYAML': 'yaml',
        'python-dotenv': 'dotenv',
        'requests': 'requests',
        'selenium': 'selenium'
    }

    all_available = True

    for display_name, import_name in required_packages.items():
        try:
            __import__(import_name)
            print(f"✅ {display_name}")
        except ImportError:
            print(f"❌ 缺少依赖: {display_name}")
            all_available = False

    return all_available


def show_installation_info():
    """显示安装信息"""
    print_header("安装信息")
    
    try:
        # 获取安装路径
        result = subprocess.run([sys.executable, '-c', 
                               'import ledvance_cli; print(ledvance_cli.__file__)'], 
                              capture_output=True, text=True, check=True)
        
        install_path = Path(result.stdout.strip()).parent
        print(f"📁 安装路径: {install_path}")
        
        # 检查安装模式
        if install_path.name == 'ledvance_cli' and 'site-packages' in str(install_path):
            print("📦 安装模式: 生产模式 (site-packages)")
        elif 'src' in str(install_path):
            print("🔧 安装模式: 开发模式 (editable)")
        else:
            print("❓ 安装模式: 未知")
        
        # 显示Python环境
        print(f"🐍 Python版本: {sys.version}")
        print(f"📍 Python路径: {sys.executable}")
        
    except Exception as e:
        print(f"❌ 无法获取安装信息: {e}")


def main():
    """主函数"""
    print("🔍 Ledvance CLI 安装验证程序")
    print("=" * 60)
    print("这个脚本将验证Ledvance CLI是否正确安装")
    
    # 执行检查步骤
    checks = [
        check_command_availability,
        check_help_system,
        check_commands,
        check_command_help,
        check_completion_feature,
        check_configuration,
        check_dependencies,
    ]
    
    results = []
    for check in checks:
        result = check()
        results.append(result)
    
    # 显示安装信息
    show_installation_info()
    
    # 总结结果
    print_header("验证结果")
    
    passed = sum(1 for r in results if r)
    total = len(results)
    
    print(f"📊 验证结果: {passed}/{total} 项通过")
    
    if passed == total:
        print("🎉 所有验证通过！Ledvance CLI安装正确且功能正常。")
        print("\n🚀 您可以开始使用以下命令:")
        print("   ledvance --help")
        print("   ledvance tuya-login")
        print("   ledvance completion bash --install")
        return 0
    else:
        print(f"⚠️  有 {total - passed} 项验证失败。")
        print("请检查安装或重新安装Ledvance CLI。")
        return 1


if __name__ == "__main__":
    sys.exit(main())
