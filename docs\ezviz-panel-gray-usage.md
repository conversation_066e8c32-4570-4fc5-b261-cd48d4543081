# ezviz-panel-gray 命令使用指南

## 概述

`ezviz-panel-gray` 命令是专门用于 Ezviz 面板灰度操作的工具。它基于您提供的原始代码进行了适配，并集成到了 Ledvance CLI 框架中。

## 功能特性

- ✅ 设置面板灰度用户
- ✅ 为特定产品设置灰度
- ✅ 取消灰度（发布面板）
- ✅ 支持批量产品操作
- ✅ 支持 dry-run 模式
- ✅ 完整的错误处理和日志记录
- ✅ 多种输出格式（text/json/yaml）

## 命令语法

```bash
ledvance ezviz-panel-gray [OPTIONS]
```

## 参数说明

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `--env` | 选择 | `Test` | 平台环境：`Prod` (生产) 或 `Test` (测试) |
| `--type` | 选择 | `Gray` | 灰度类型：`Gray` (设置灰度) 或 `Release` (取消灰度) |
| `--name` | 文本 | **必需** | RN包名称 |
| `--pids` | 文本 | 可选 | 产品ID列表，用逗号分隔 |
| `--users` | 文本 | 可选 | 灰度账号列表，用逗号分隔 |

## 使用示例

### 1. 基本灰度操作

为整个面板设置灰度：

```bash
# 使用默认灰度用户
ledvance ezviz-panel-gray --name "my-rn-panel" --type Gray

# 指定灰度用户
ledvance ezviz-panel-gray --name "my-rn-panel" --type Gray --users "<EMAIL>,<EMAIL>"
```

### 2. 特定产品灰度操作

为特定产品设置灰度：

```bash
# 为单个产品设置灰度
ledvance ezviz-panel-gray --name "my-rn-panel" --type Gray --pids "TY_LDV_001"

# 为多个产品设置灰度
ledvance ezviz-panel-gray --name "my-rn-panel" --type Gray --pids "TY_LDV_001,TY_LDV_002,TY_LDV_003"
```

### 3. 发布操作（取消灰度）

```bash
# 发布整个面板
ledvance ezviz-panel-gray --name "my-rn-panel" --type Release

# 发布特定产品
ledvance ezviz-panel-gray --name "my-rn-panel" --type Release --pids "TY_LDV_001"
```

### 4. 生产环境操作

```bash
# 在生产环境中操作
ledvance ezviz-panel-gray --env Prod --name "my-rn-panel" --type Gray --users "<EMAIL>"
```

### 5. Dry-run 模式

在实际执行前预览操作：

```bash
# 预览灰度操作
ledvance --dry-run ezviz-panel-gray --name "my-rn-panel" --type Gray --users "<EMAIL>"

# 预览发布操作
ledvance --dry-run ezviz-panel-gray --name "my-rn-panel" --type Release
```

### 6. 不同输出格式

```bash
# JSON 格式输出
ledvance --output json ezviz-panel-gray --name "my-rn-panel" --type Gray

# YAML 格式输出
ledvance --output yaml ezviz-panel-gray --name "my-rn-panel" --type Gray
```

## 配置文件支持

命令支持从配置文件读取默认值：

```yaml
# config.yaml
gray_accounts:
  - <EMAIL>
  - <EMAIL>
  - <EMAIL>
```

## 与原始代码的对应关系

您提供的原始代码功能已完全适配：

| 原始代码功能 | 新命令对应 |
|-------------|-----------|
| `args.env` | `--env` 参数 |
| `args.type` | `--type` 参数 |
| `args.name` | `--name` 参数 |
| `args.pids` | `--pids` 参数 |
| `args.users` | `--users` 参数 |
| `GRAY_ACCOUNTS` | 配置文件中的 `gray_accounts` |
| `panel_gray()` 函数 | `_panel_gray_operation()` 函数 |

## 错误处理

命令包含完整的错误处理：

- ✅ 面板不存在时的错误提示
- ✅ API 调用失败时的重试机制
- ✅ 网络错误的友好提示
- ✅ 参数验证和错误提示

## 日志记录

命令提供详细的日志记录：

- 📝 操作进度跟踪
- 📝 API 调用结果
- 📝 错误详情记录
- 📝 成功/失败统计

## 最佳实践

1. **先使用 dry-run 模式**：在实际操作前先预览
2. **分批处理**：对于大量产品，建议分批操作
3. **检查配置**：确保配置文件中的默认账号正确
4. **监控日志**：关注操作日志以及时发现问题
5. **备份重要数据**：在生产环境操作前做好备份

## 故障排除

### 常见问题

1. **面板未找到**
   ```
   解决方案：检查面板名称是否正确，确认面板在指定环境中存在
   ```

2. **API 调用失败**
   ```
   解决方案：检查网络连接，确认环境配置正确
   ```

3. **权限不足**
   ```
   解决方案：确认账号有足够权限进行灰度操作
   ```

## 技术细节

- 基于 Click 框架构建
- 支持自动命令发现
- 集成到 Ledvance CLI 生态系统
- 遵循项目的代码规范和架构模式
