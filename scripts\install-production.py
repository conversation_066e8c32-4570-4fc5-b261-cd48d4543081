#!/usr/bin/env python3
"""
Ledvance CLI 生产环境安装脚本

这个脚本会自动安装Ledvance CLI到生产环境，包括依赖检查、安装验证和自动补全配置。
"""

import os
import platform
import subprocess
import sys
import tempfile
from pathlib import Path


def print_header(title):
    """打印标题"""
    print(f"\n{'='*60}")
    print(f"🚀 {title}")
    print('='*60)


def print_step(step, description):
    """打印步骤"""
    print(f"\n{step} {description}")
    print("-" * 40)


def check_python_version():
    """检查Python版本"""
    print_step("1️⃣", "检查Python版本")
    
    version = sys.version_info
    print(f"当前Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version < (3, 8):
        print("❌ 错误: 需要Python 3.8或更高版本")
        print("请升级Python版本后重试")
        return False
    
    print("✅ Python版本符合要求")
    return True


def check_pip():
    """检查pip是否可用"""
    print_step("2️⃣", "检查pip工具")
    
    try:
        result = subprocess.run([sys.executable, '-m', 'pip', '--version'], 
                              capture_output=True, text=True, check=True)
        print(f"pip版本: {result.stdout.strip()}")
        print("✅ pip工具可用")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ 错误: pip工具不可用")
        print("请安装pip后重试")
        return False


def install_build_tools():
    """安装构建工具"""
    print_step("3️⃣", "安装构建工具")
    
    try:
        print("📦 安装build工具...")
        subprocess.run([sys.executable, '-m', 'pip', 'install', 'build'], 
                      check=True)
        print("✅ 构建工具安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 构建工具安装失败: {e}")
        return False


def install_ledvance_cli(install_mode='production'):
    """安装Ledvance CLI"""
    print_step("4️⃣", f"安装Ledvance CLI ({install_mode}模式)")
    
    try:
        if install_mode == 'production':
            # 生产模式安装
            print("📦 安装Ledvance CLI (生产模式)...")
            subprocess.run([sys.executable, '-m', 'pip', 'install', '.'], 
                          check=True)
        elif install_mode == 'development':
            # 开发模式安装
            print("📦 安装Ledvance CLI (开发模式)...")
            subprocess.run([sys.executable, '-m', 'pip', 'install', '-e', '.'], 
                          check=True)
        else:
            raise ValueError(f"不支持的安装模式: {install_mode}")
        
        print("✅ Ledvance CLI安装成功")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Ledvance CLI安装失败: {e}")
        return False


def verify_installation():
    """验证安装"""
    print_step("5️⃣", "验证安装")
    
    try:
        # 检查命令是否可用
        result = subprocess.run(['ledvance', '--version'], 
                              capture_output=True, text=True, check=True)
        print(f"✅ 命令可用: {result.stdout.strip()}")
        
        # 检查帮助信息
        result = subprocess.run(['ledvance', '--help'], 
                              capture_output=True, text=True, check=True)
        
        # 统计可用命令数量
        help_lines = result.stdout.split('\n')
        commands = []
        in_commands = False
        
        for line in help_lines:
            if 'Commands:' in line:
                in_commands = True
                continue
            elif in_commands and line.strip():
                if line.startswith('  ') and not line.startswith('    '):
                    cmd = line.strip().split()[0]
                    commands.append(cmd)
        
        print(f"✅ 发现 {len(commands)} 个可用命令:")
        for cmd in commands:
            print(f"   • {cmd}")
        
        return True
        
    except (subprocess.CalledProcessError, FileNotFoundError) as e:
        print(f"❌ 安装验证失败: {e}")
        return False


def setup_completion():
    """设置自动补全"""
    print_step("6️⃣", "配置自动补全")
    
    try:
        # 检查是否支持自动补全
        result = subprocess.run(['ledvance', 'completion', '--help'], 
                              capture_output=True, text=True, check=True)
        print("✅ 自动补全功能可用")
        
        # 询问是否安装自动补全
        response = input("是否安装shell自动补全? (y/N): ").strip().lower()
        
        if response in ['y', 'yes']:
            # 运行自动补全安装脚本
            try:
                subprocess.run([sys.executable, 'scripts/install-completion.py'], 
                              check=True)
                print("✅ 自动补全配置成功")
            except subprocess.CalledProcessError:
                print("⚠️  自动补全配置失败，可以稍后手动配置")
                print("   运行: python scripts/install-completion.py")
        else:
            print("⏭️  跳过自动补全配置")
            print("   稍后可运行: ledvance completion <shell> --install")
        
        return True
        
    except subprocess.CalledProcessError:
        print("⚠️  自动补全功能不可用")
        return False


def create_config_template():
    """创建配置文件模板"""
    print_step("7️⃣", "创建配置文件")
    
    config_locations = [
        Path.cwd() / 'config.yaml',
        Path.home() / '.config' / 'ledvance' / 'config.yaml',
        Path.home() / '.ledvance' / 'config.yaml'
    ]
    
    # 检查是否已有配置文件
    existing_config = None
    for config_path in config_locations:
        if config_path.exists():
            existing_config = config_path
            break
    
    if existing_config:
        print(f"✅ 发现现有配置文件: {existing_config}")
        return True
    
    # 询问是否创建配置文件
    response = input("是否创建配置文件模板? (y/N): ").strip().lower()
    
    if response in ['y', 'yes']:
        try:
            # 检查是否有示例配置文件
            example_config = Path.cwd() / 'config.yaml.example'
            target_config = Path.cwd() / 'config.yaml'
            
            if example_config.exists():
                import shutil
                shutil.copy2(example_config, target_config)
                print(f"✅ 配置文件已创建: {target_config}")
                print("💡 请编辑配置文件以适应您的环境")
            else:
                print("⚠️  未找到配置文件模板")
                
        except Exception as e:
            print(f"❌ 创建配置文件失败: {e}")
    else:
        print("⏭️  跳过配置文件创建")
    
    return True


def show_usage_guide():
    """显示使用指南"""
    print_header("使用指南")
    
    print("🎉 安装完成！以下是一些使用示例:")
    print()
    print("📋 基本命令:")
    print("   ledvance --help                    # 查看帮助")
    print("   ledvance --version                 # 查看版本")
    print("   ledvance tuya-login                # 涂鸦平台登录")
    print("   ledvance ezviz-panel-check --help  # 查看面板检查帮助")
    print()
    print("🔧 配置:")
    print("   编辑 config.yaml 文件配置环境参数")
    print("   设置环境变量覆盖配置")
    print()
    print("📚 更多信息:")
    print("   docs/installation.md     # 详细安装指南")
    print("   docs/shell-completion.md # 自动补全指南")
    print("   README.md                # 项目说明")


def main():
    """主函数"""
    print("🚀 Ledvance CLI 生产环境安装程序")
    print("=" * 60)
    print("这个脚本将安装Ledvance CLI到您的系统中")
    
    # 询问安装模式
    print("\n📦 选择安装模式:")
    print("1. 生产模式 (推荐) - 正式使用")
    print("2. 开发模式 - 开发调试")
    
    while True:
        choice = input("\n请选择 (1/2): ").strip()
        if choice == '1':
            install_mode = 'production'
            break
        elif choice == '2':
            install_mode = 'development'
            break
        else:
            print("请输入 1 或 2")
    
    print(f"\n选择的安装模式: {install_mode}")
    
    # 执行安装步骤
    steps = [
        check_python_version,
        check_pip,
        lambda: install_ledvance_cli(install_mode),
        verify_installation,
        setup_completion,
        create_config_template,
    ]
    
    for step in steps:
        if not step():
            print(f"\n❌ 安装失败，请检查错误信息")
            return 1
    
    # 显示使用指南
    show_usage_guide()
    
    print(f"\n🎉 Ledvance CLI ({install_mode}模式) 安装成功!")
    return 0


if __name__ == "__main__":
    sys.exit(main())
