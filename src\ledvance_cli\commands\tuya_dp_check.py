"""
检查涂鸦产品是否包含特定 DP Code 的命令。
"""
import logging

import click

from ledvance_cli.core import tuya_api
from ledvance_cli.core.exceptions import CLIError
from ledvance_cli.utils import format_output, show_progress

logger = logging.getLogger(__name__)


def _fetch_all_products(base_payload: dict):
    """
    获取所有产品，自动处理分页。
    """
    all_products = []
    current_page = 1
    page_size = 200  # 使用较大的页面大小以减少请求次数

    logger.info("正在从涂鸦平台获取所有产品，这可能需要一些时间...")

    while True:
        # 复制基础 payload 并更新分页参数
        payload = base_payload.copy()
        payload["searchParam"]["pageNo"] = current_page
        payload["searchParam"]["pageSize"] = page_size

        try:
            products, total = tuya_api.page_product_list(payload)
        except CLIError as e:
            logger.error(f"获取产品时出错: {e}")
            break

        if not products:
            logger.info("已获取所有产品。")
            break

        all_products.extend(products)
        total_pages = (total + page_size - 1) // page_size
        logger.info(f"已获取第 {current_page}/{total_pages} 页，累计 {len(all_products)}/{total} 个产品...")

        if current_page >= total_pages:
            break
        current_page += 1

    logger.info(f"数据获取完成，共 {len(all_products)} 条产品记录。")
    return all_products


@click.command("tuya-dp-check")
@click.option("--category", default="", help="要筛选的涂鸦产品分类。")
@click.option("--type", "query_type", default="name", help="查询类型：'id' 或 'name'。")
@click.option("--keyword", default="", help="用于筛选的查询关键字。")
@click.option("--dp", "dp_code", required=True, help="要检查的 DP code (例如 'switch_led')。")
@click.pass_context
def tuya_dp_check(ctx: click.Context, category: str, query_type: str, keyword: str, dp_code: str):
    """
    遍历指定范围内的所有产品，检查它们是否包含某个特定的、已选中的 DP Code。
    """
    output_format = ctx.obj.output_format
    is_dry_run = ctx.obj.dry_run

    logger.info(f"开始检查所有产品是否包含已选中的 DP Code: '{dp_code}'")
    if is_dry_run:
        logger.info("🔍 [DRY-RUN] 运行在模拟模式")

    # 构建基础 API 请求 payload
    base_payload = {
        "searchParam": {
            "orderType": "createDesc",
            "type": query_type,
            "category": category,
            "keyword": keyword,
            "tagIDList": ""
        }
    }

    if is_dry_run:
        logger.info("🔍 [DRY-RUN] 模拟API调用: _fetch_all_products")
        # 创建模拟的产品列表
        products = [
            {
                "id": f"TY_LDV_mock_{i:03d}",
                "name": f"Mock Product {i}",
                "category": category or "LightSource",
                "createTime": 1640995200000 + i * 86400000
            }
            for i in range(1, 6)  # 模拟5个产品
        ]
    else:
        products = _fetch_all_products(base_payload)

    if not products:
        logger.warning("未找到任何产品，检查结束。")
        return

    found_products = []
    # 使用进度条来显示检查进度
    for product in show_progress(products, description="正在检查产品 DP..."):
        try:
            if is_dry_run:
                # 创建模拟的DP数据，随机决定是否包含目标DP
                import random
                has_target_dp = random.choice([True, False])
                if has_target_dp:
                    dps = [
                        {"code": "switch_led", "selected": True, "name": "开关"},
                        {"code": dp_code, "selected": True, "name": f"模拟DP-{dp_code}"}
                    ]
                else:
                    dps = [
                        {"code": "switch_led", "selected": True, "name": "开关"},
                        {"code": "bright_value", "selected": False, "name": "亮度"}
                    ]
            else:
                dps = tuya_api.get_product_dp(product['id'])

            # 检查是否有任何 DP 的 code 匹配且被选中
            has_dp = any(
                item.get('code') == dp_code and item.get('selected')
                for item in dps
            )
            if has_dp:
                logger.info(f"找到匹配项：产品 '{product['name']}' (ID: {product['id']}) 包含 DP '{dp_code}'。")
                found_products.append({
                    "product_name": product["name"],
                    "product_id": product["id"],
                    "dp_code": dp_code,
                })
        except CLIError as e:
            logger.error(f"检查产品 {product['id']} 时出错: {e}")
            continue # 继续检查下一个产品

    # 输出结果
    if found_products:
        format_output(
            found_products,
            output_format,
            title=f"包含已选中 DP '{dp_code}' 的产品列表"
        )
    else:
        logger.info(f"检查完成，没有找到任何包含已选中 DP '{dp_code}' 的产品。")
