"""
Ezviz 面板创建命令 - 创建新的面板。
"""
import json
import logging
from pathlib import Path
from typing import Optional

import click

from ledvance_cli import config as app_config
from ledvance_cli.core import ezviz_api
from ledvance_cli.core.exceptions import CLIError
from ledvance_cli.utils import format_output, read_md5

logger = logging.getLogger(__name__)


@click.command("ezviz-panel-create")
@click.option(
    "--env",
    default="Test",
    type=click.Choice(['Prod', 'Test'], case_sensitive=True),
    help="要操作的平台环境：'Prod' (生产) 或 'Test' (测试)。"
)
@click.option(
    "--type",
    "panel_type",
    default="Standard",
    type=click.Choice(['Standard', 'Custom'], case_sensitive=True),
    help="面板类型：'Standard' (标准) 或 'Custom' (自定义)。"
)
@click.option(
    "--base",
    help="基于面板名称。如果未指定，使用 --rn-name 的值。"
)
@click.option(
    "--name",
    required=True,
    help="面板名称。"
)
@click.option(
    "--rn-name",
    required=True,
    help="RN包名。"
)
@click.option(
    "--version",
    default="V1.0.0",
    help="RN版本。"
)
@click.option(
    "--app-version",
    default="1.0.0",
    help="App版本。"
)
@click.option(
    "--desc",
    default="",
    help="描述。"
)
@click.option(
    "--main-category",
    help="一级品类。"
)
@click.option(
    "--category",
    help="二级品类。"
)
@click.option(
    "--pid",
    help="产品ID（自定义面板必需）。"
)
@click.option(
    "--dir",
    "base_dir",
    type=click.Path(exists=True, path_type=Path),
    help="包所在目录。如果未指定，使用配置中的 base_dir。"
)
@click.pass_context
def ezviz_panel_create(
    ctx: click.Context,
    env: str,
    panel_type: str,
    base: Optional[str],
    name: str,
    rn_name: str,
    version: str,
    app_version: str,
    desc: str,
    main_category: Optional[str],
    category: Optional[str],
    pid: Optional[str],
    base_dir: Optional[Path]
):
    """
    Ezviz 面板创建命令 - 创建新的面板。

    此命令支持创建标准面板和自定义面板：
    1. 检查 RN 名称是否重复
    2. 上传相关文件（海报图片、Android/iOS 包）
    3. 创建面板
    4. 设置面板为非灰度状态（直接发布）
    """
    output_format = ctx.obj.output_format
    is_dry_run = ctx.obj.dry_run

    # 验证参数
    if panel_type == 'Custom' and not pid:
        raise CLIError("自定义面板必须指定产品ID (--pid)")

    if is_dry_run:
        logger.info("🔍 [DRY-RUN] Ezviz面板创建操作预览")
        logger.info(f"🌐 目标环境: {env}")
        logger.info(f"🔧 面板类型: {panel_type}")
        logger.info(f"📦 面板名称: {name}")
        logger.info(f"📦 RN包名: {rn_name}")
        logger.info(f"📦 版本: {version}")
        logger.info(f"📱 App版本: {app_version}")
        logger.info(f"📁 基础目录: {base_dir if base_dir else '使用配置默认值'}")
        logger.info(f"🏷️ 一级品类: {main_category if main_category else '未指定'}")
        logger.info(f"🏷️ 二级品类: {category if category else '未指定'}")
        if panel_type == 'Custom':
            logger.info(f"🆔 产品ID: {pid}")
        logger.info("🔄 将要执行的操作:")
        logger.info("  1. 检查 RN 名称是否重复")
        logger.info("  2. 上传海报图片")
        logger.info("  3. 上传 Android 和 iOS 包")
        logger.info("  4. 创建面板")
        logger.info("  5. 设置面板为发布状态")
        logger.info("💡 注意: 在dry-run模式下，不会进行实际的API调用和文件操作")

        # 在 dry-run 模式下也执行完整流程以显示详细步骤
        if is_dry_run:
            logger.info("🔄 开始执行 dry-run 模拟流程...")
        else:
            return

    logger.info(f"开始面板创建操作 (环境: {env}, 类型: {panel_type})")

    try:
        # 获取基础目录
        if not base_dir:
            base_dir = Path(app_config.APP_CONFIG.get("base_dir", "."))
        logger.info(f"使用基础目录: {base_dir}")

        # 确定基础名称
        base_name = base if base else rn_name
        logger.info(f"使用基础名称: {base_name}")

        # 创建面板
        result = _create_panel(
            env=env,
            panel_type=panel_type,
            base_name=base_name,
            name=name,
            rn_name=rn_name,
            version=version,
            app_version=app_version,
            desc=desc,
            main_category=main_category,
            category=category,
            pid=pid,
            base_dir=base_dir,
            is_dry_run=is_dry_run
        )

        # 输出结果
        if output_format != "text":
            format_output([{
                'panel_name': name,
                'rn_name': rn_name,
                'success': result is not None,
                'panel_id': result.get('panelId') if result else None
            }], output_format, title=f"面板创建结果 ({env.upper()})")

        if result:
            logger.info(f"面板创建成功，面板ID: {result.get('panelId')}")
        else:
            logger.error("面板创建失败")
            ctx.exit(1)

    except CLIError as e:
        logger.error(f"面板创建操作时出错: {e}")
        ctx.exit(1)
    except Exception as e:
        logger.error(f"发生意外错误: {e}", exc_info=True)
        ctx.exit(1)

    logger.info("面板创建操作完成。")


def _create_panel(
    env: str,
    panel_type: str,
    base_name: str,
    name: str,
    rn_name: str,
    version: str,
    app_version: str,
    desc: str,
    main_category: Optional[str],
    category: Optional[str],
    pid: Optional[str],
    base_dir: Path,
    is_dry_run: bool
) -> Optional[dict]:
    """
    创建面板的核心逻辑。

    Returns:
        创建成功时返回面板信息，失败时返回 None
    """
    try:
        # 1. 检查 RN 名称是否重复
        logger.info(f"检查 RN 名称 '{rn_name}' 是否重复...")
        if is_dry_run:
            logger.info("🔍 [DRY-RUN] 模拟检查 RN 名称")
            name_exists = False
        else:
            name_exists = ezviz_api.check_rn_name(rn_name, env=env)

        if name_exists:
            logger.error(f"RN 名称 '{rn_name}' 已存在，无法创建")
            return None

        logger.info("RN 名称检查通过")

        # 2. 上传海报图片
        logger.info(f"上传海报图片...")
        poster_file = f'poster/{base_name}.PNG'
        if is_dry_run:
            logger.info(f"🔍 [DRY-RUN] 模拟上传海报图片: {poster_file}")
            picture_url = f"mock_picture_url_{base_name}"
        else:
            poster_path = base_dir / poster_file
            if not poster_path.exists():
                logger.error(f"海报图片不存在: {poster_path}")
                return None
            picture_url = ezviz_api.upload_file(poster_file, env=env)
            if not picture_url:
                logger.error("海报图片上传失败")
                return None

        logger.info("海报图片上传成功")

        # 3. 上传 Android 和 iOS 包
        logger.info("上传 Android 和 iOS 包...")
        files = [f'cloud/{base_name}-android.zip', f'cloud/{base_name}-ios.zip']
        configs = []

        for file_name in files:
            if is_dry_run:
                logger.info(f"🔍 [DRY-RUN] 模拟上传文件: {file_name}")
                configs.append(f"mock_config_url_{file_name}")
            else:
                file_path = base_dir / file_name
                if not file_path.exists():
                    logger.error(f"文件不存在: {file_path}")
                    return None

                config_url = ezviz_api.upload_file(file_name, env=env)
                if not config_url:
                    logger.error(f"文件 {file_name} 上传失败")
                    return None
                configs.append(config_url)

        android_config, ios_config = configs
        logger.info("Android 和 iOS 包上传成功")

        # 4. 准备 EIB 配置
        if is_dry_run:
            logger.info("🔍 [DRY-RUN] 模拟读取 MD5 文件")
            eib_config = {
                'androidMd5': f'mock_android_md5_{base_name}',
                'iosMd5': f'mock_ios_md5_{base_name}',
            }
        else:
            eib_config = {
                'androidMd5': read_md5(f'{base_dir}/cloud/{base_name}-android.md5'),
                'iosMd5': read_md5(f'{base_dir}/cloud/{base_name}-ios.md5'),
            }

        # 5. 构建创建面板的载荷
        payload = {
            'panelType': 0 if panel_type == 'Standard' else 1,
            'panelName': name,
            'appVersion': app_version,
            'androidConfig': android_config,
            'iosConfig': ios_config,
            'eibConfig': json.dumps(eib_config),
            'picture': picture_url,
            'pversion': 'V1.0.0',
            'rnDesc': desc,
            'rnName': rn_name,
            'version': version,
            'firstCategory': main_category or '',
            'secondCategory': category or '',
        }

        if panel_type == 'Custom' and pid:
            payload['pid'] = pid

        # 6. 创建面板
        logger.info("创建面板...")
        if is_dry_run:
            logger.info("🔍 [DRY-RUN] 模拟创建面板")
            panel_result = {'panelId': f'mock_panel_id_{rn_name}'}
        else:
            panel_result = ezviz_api.create_panel(payload, env=env)

        if not panel_result:
            logger.error("面板创建失败")
            return None

        logger.info("面板创建成功")

        # 7. 设置面板为发布状态（非灰度）
        logger.info("设置面板为发布状态...")
        if is_dry_run:
            logger.info("🔍 [DRY-RUN] 模拟设置面板发布状态")
            release_result = True
        else:
            gated_param = {
                "panelId": panel_result['panelId'],
                "isGated": False
            }
            release_result = ezviz_api.update_gray_info(gated_param, env=env)

        if release_result:
            logger.info("面板发布成功")
        else:
            logger.warning("面板发布失败，但面板已创建")

        return panel_result

    except Exception as e:
        logger.error(f"创建面板时出错: {e}")
        return None
