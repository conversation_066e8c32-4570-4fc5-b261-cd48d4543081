#!/usr/bin/env python3
"""
测试 ezviz-panel-set 命令的脚本
"""

import subprocess
import sys
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def run_command(cmd_args, description):
    """运行命令并检查结果"""
    logger.info(f"🧪 测试: {description}")
    logger.info(f"📝 命令: {' '.join(cmd_args)}")
    
    try:
        result = subprocess.run(
            cmd_args,
            capture_output=True,
            text=True,
            timeout=30
        )
        
        if result.returncode == 0:
            logger.info("✅ 命令执行成功")
            return True
        else:
            logger.error(f"❌ 命令执行失败 (退出码: {result.returncode})")
            logger.error(f"错误输出: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        logger.error("❌ 命令执行超时")
        return False
    except Exception as e:
        logger.error(f"❌ 命令执行异常: {e}")
        return False


def main():
    """主测试函数"""
    logger.info("🚀 开始测试 ezviz-panel-set 命令")
    
    test_cases = [
        {
            "description": "显示帮助信息",
            "command": ["ledvance", "ezviz-panel-set", "--help"]
        },
        {
            "description": "单个产品面板设置 (dry-run)",
            "command": [
                "ledvance", "--dry-run", "ezviz-panel-set",
                "--name", "test-panel",
                "--pids", "TY_LDV_001"
            ]
        },
        {
            "description": "多个产品面板设置 (dry-run)",
            "command": [
                "ledvance", "--dry-run", "ezviz-panel-set",
                "--name", "multi-test-panel",
                "--pids", "TY_LDV_001,TY_LDV_002,TY_LDV_003"
            ]
        },
        {
            "description": "生产环境面板设置 (dry-run)",
            "command": [
                "ledvance", "--dry-run", "ezviz-panel-set",
                "--env", "Prod",
                "--name", "prod-panel",
                "--pids", "TY_LDV_prod001,TY_LDV_prod002"
            ]
        },
        {
            "description": "JSON 输出格式 (dry-run)",
            "command": [
                "ledvance", "--dry-run", "--output", "json", "ezviz-panel-set",
                "--name", "json-test-panel",
                "--pids", "TY_LDV_json001,TY_LDV_json002"
            ]
        },
        {
            "description": "YAML 输出格式 (dry-run)",
            "command": [
                "ledvance", "--dry-run", "--output", "yaml", "ezviz-panel-set",
                "--name", "yaml-test-panel",
                "--pids", "TY_LDV_yaml001"
            ]
        },
        {
            "description": "大量产品面板设置 (dry-run)",
            "command": [
                "ledvance", "--dry-run", "ezviz-panel-set",
                "--name", "bulk-test-panel",
                "--pids", "TY_LDV_001,TY_LDV_002,TY_LDV_003,TY_LDV_004,TY_LDV_005"
            ]
        }
    ]
    
    # 错误测试用例
    error_test_cases = [
        {
            "description": "缺少必需参数 --name",
            "command": ["ledvance", "--dry-run", "ezviz-panel-set", "--pids", "TY_LDV_001"],
            "should_fail": True
        },
        {
            "description": "缺少必需参数 --pids",
            "command": ["ledvance", "--dry-run", "ezviz-panel-set", "--name", "test"],
            "should_fail": True
        },
        {
            "description": "空的产品ID列表",
            "command": [
                "ledvance", "--dry-run", "ezviz-panel-set",
                "--name", "test",
                "--pids", ""
            ],
            "should_fail": True
        },
        {
            "description": "只有逗号的产品ID列表",
            "command": [
                "ledvance", "--dry-run", "ezviz-panel-set",
                "--name", "test",
                "--pids", ",,,"
            ],
            "should_fail": True
        }
    ]
    
    success_count = 0
    total_tests = len(test_cases) + len(error_test_cases)
    
    # 运行正常测试用例
    for test_case in test_cases:
        if run_command(test_case["command"], test_case["description"]):
            success_count += 1
        print()  # 空行分隔
    
    # 运行错误测试用例
    logger.info("🔍 开始测试错误处理...")
    for test_case in error_test_cases:
        result = run_command(test_case["command"], test_case["description"])
        # 对于应该失败的测试，失败是成功的
        if test_case.get("should_fail", False):
            if not result:
                logger.info("✅ 错误处理正确")
                success_count += 1
            else:
                logger.error("❌ 应该失败但成功了")
        else:
            if result:
                success_count += 1
        print()  # 空行分隔
    
    # 输出测试结果
    logger.info("=" * 60)
    logger.info(f"📊 测试完成: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        logger.info("🎉 所有测试通过！")
        return 0
    else:
        logger.error(f"❌ {total_tests - success_count} 个测试失败")
        return 1


if __name__ == "__main__":
    sys.exit(main())
