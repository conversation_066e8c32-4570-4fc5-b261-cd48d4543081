"""
CLI 应用程序的配置管理。

从多层级 YAML 文件和环境变量中加载设置。
支持按优先级顺序从多个位置读取配置文件。
"""

import logging
import os
import platform
from pathlib import Path
from typing import Any, Dict, List, Optional

import yaml
from dotenv import load_dotenv

logger = logging.getLogger(__name__)

class Config:
    """
    配置管理器，支持多层级配置文件读取和环境变量覆盖。

    按照以下优先级顺序读取配置：
    1. 系统配置目录（最低优先级）
    2. 用户主目录配置
    3. 当前工作目录配置（最高优先级）
    4. 环境变量覆盖（最高优先级）
    """

    def __init__(self, default_config: Optional[Dict[str, Any]] = None):
        """
        初始化配置管理器。

        Args:
            default_config: 包含默认配置值的字典。
        """
        self.config: Dict[str, Any] = default_config or {}
        self.loaded_config_files: List[Path] = []
        self.loaded_env_files: List[Path] = []

        # 按优先级顺序加载配置
        self._load_all_configs()
        self._override_with_env_vars()

    def _get_config_paths(self) -> List[Path]:
        """
        获取按优先级排序的配置文件路径列表。

        Returns:
            配置文件路径列表，从低优先级到高优先级排序。
        """
        paths = []

        # 1. 系统配置目录（最低优先级）
        system_config = self._get_system_config_path()
        if system_config:
            paths.append(system_config)

        # 2. 用户主目录配置
        user_configs = self._get_user_config_paths()
        paths.extend(user_configs)

        # 3. 当前工作目录配置（最高优先级）
        current_config = Path.cwd() / "config.yaml"
        paths.append(current_config)

        return paths

    def _get_env_paths(self) -> List[Path]:
        """
        获取按优先级排序的 .env 文件路径列表。

        Returns:
            .env 文件路径列表，从低优先级到高优先级排序。
        """
        paths = []

        # 1. 系统配置目录（最低优先级）
        system_env = self._get_system_config_path(filename=".env")
        if system_env:
            paths.append(system_env)

        # 2. 用户主目录配置
        user_envs = self._get_user_config_paths(filename=".env")
        paths.extend(user_envs)

        # 3. 当前工作目录配置（最高优先级）
        current_env = Path.cwd() / ".env"
        paths.append(current_env)

        return paths

    def _get_system_config_path(self, filename: str = "config.yaml") -> Optional[Path]:
        """获取系统配置目录路径。"""
        system = platform.system().lower()

        if system == "windows":
            # Windows: %PROGRAMDATA%\ledvance\
            program_data = os.getenv("PROGRAMDATA")
            if program_data:
                return Path(program_data) / "ledvance" / filename
        else:
            # Linux/macOS: /etc/ledvance/
            return Path("/etc/ledvance") / filename

        return None

    def _get_user_config_paths(self, filename: str = "config.yaml") -> List[Path]:
        """获取用户配置目录路径列表。"""
        paths = []
        home = Path.home()

        # ~/.ledvance/
        ledvance_dir = home / ".ledvance" / filename
        paths.append(ledvance_dir)

        # ~/.config/ledvance/ (XDG 标准)
        config_dir = home / ".config" / "ledvance" / filename
        paths.append(config_dir)

        return paths

    def _load_all_configs(self) -> None:
        """按优先级顺序加载所有配置文件和环境变量文件。"""
        # 1. 先加载所有 .env 文件（从低优先级到高优先级）
        self._load_all_env_files()

        # 2. 再加载所有配置文件（从低优先级到高优先级）
        self._load_all_config_files()

    def _load_all_env_files(self) -> None:
        """按优先级顺序加载所有 .env 文件。"""
        env_paths = self._get_env_paths()

        for env_path in env_paths:
            if env_path.exists():
                try:
                    load_dotenv(dotenv_path=env_path, override=True)
                    self.loaded_env_files.append(env_path)
                    logger.debug(f"从 {env_path} 加载了环境变量。")
                except Exception as e:
                    logger.warning(f"加载环境变量文件 {env_path} 时出错: {e}")

    def _load_all_config_files(self) -> None:
        """按优先级顺序加载所有配置文件。"""
        config_paths = self._get_config_paths()

        for config_path in config_paths:
            if config_path.exists():
                try:
                    with open(config_path, "r", encoding="utf-8") as f:
                        file_config = yaml.safe_load(f)

                    if file_config and isinstance(file_config, dict):
                        # 深度合并配置
                        self._deep_merge_config(self.config, file_config)
                        self.loaded_config_files.append(config_path)
                        logger.debug(f"从 {config_path} 加载了配置")
                    else:
                        logger.warning(f"配置文件 {config_path} 为空或格式无效")

                except (IOError, yaml.YAMLError) as e:
                    logger.error(f"加载配置文件 {config_path} 时出错: {e}")
                    # 继续加载其他配置文件，不中断流程

        # 记录实际加载的配置文件
        if self.loaded_config_files:
            logger.info(f"已加载配置文件: {[str(p) for p in self.loaded_config_files]}")
        else:
            logger.warning("未找到任何配置文件，使用默认设置。")

        if self.loaded_env_files:
            logger.info(f"已加载环境变量文件: {[str(p) for p in self.loaded_env_files]}")

    def _deep_merge_config(self, target: Dict[str, Any], source: Dict[str, Any]) -> None:
        """
        深度合并配置字典。

        Args:
            target: 目标字典（会被修改）
            source: 源字典
        """
        for key, value in source.items():
            if key in target and isinstance(target[key], dict) and isinstance(value, dict):
                # 递归合并嵌套字典
                self._deep_merge_config(target[key], value)
            else:
                # 直接覆盖或添加新键
                target[key] = value



    def _override_with_env_vars(self) -> None:
        """使用环境变量覆盖配置。"""
        for key, value in self.config.items():
            env_var = os.getenv(key.upper())
            if env_var:
                self.config[key] = env_var
                logger.debug(f"使用环境变量覆盖了 '{key}'。")

    def get(self, key: str, default: Optional[Any] = None) -> Any:
        """
        获取一个配置值。

        Args:
            key: 要检索的配置键。
            default: 如果未找到键，则返回的默认值。

        Returns:
            配置值。
        """
        return self.config.get(key, default)

    def __getitem__(self, key: str) -> Any:
        """允许以字典方式访问配置。"""
        return self.config[key]

    def __repr__(self) -> str:
        return f"Config(path={self.config_path}, config={self.config})"

# 全局配置实例
# 可在主 CLI 入口点初始化
APP_CONFIG: Optional[Config] = None
PROD_ENV = "Prod"
TEST_ENV = "Test"
UPGRADE_TYPE = "Upgrade"
RELEASE_TYPE = "Release"
GRAY_TYPE = "Gray"
STATUS_RELEASED = 0
STATUS_NEW_VERSION = 10
STATUS_GRAYSCALE = 128

def initialize_config() -> None:
    """初始化全局配置对象，使用多层级配置文件读取。"""
    global APP_CONFIG
    if APP_CONFIG is None:
        # 在此处定义所有预期的配置键及其默认值
        default_settings = {
            "app_name": "LedvanceCLI",
            "retry": {"attempts": 3, "delay": 1},
            "tuya_host": "https://platform.tuya.com", # 为涂鸦平台的主机设置一个默认占位符，以便从环境中加载
            "tuya_username": None, # 为涂鸦平台的用户名设置一个默认占位符，以便从环境中加载
            "tuya_password": None, # 为涂鸦平台的密码设置一个默认占位符，以便从环境中加载
            "ezviz_app_host": None, # 为 Ezviz App 的主机设置一个默认占位符，以便从环境中加载
            "ezviz_host_prod": None, # 为 Ezviz 生产环境的主机设置一个默认占位符，以便从环境中加载
            "ezviz_account_prod": None, # 为 Ezviz 生产环境的账户设置一个默认占位符，以便从环境中加载
            "ezviz_password_prod": None, # 为 Ezviz 生产环境的密码设置一个默认占位符，以便从环境中加载
            "ezviz_host_test": None, # 为 Ezviz 测试环境的主机设置一个默认占位符，以便从环境中加载
            "ezviz_account_test": None, # 为 Ezviz 测试环境的账户设置一个默认占位符，以便从环境中加载
            "ezviz_password_test": None, # 为 Ezviz 测试环境的密码设置一个默认占位符，以便从环境中加载
            "temp_img_dir": "/tmp/ledvance_images",
            "tuya_img_host": "https://images.tuyacn.com",
            "ezviz_app_request_body_gray": None,
            "ezviz_app_request_body_release": None,
            "base_dir": ".",  # 基础目录，用于文件输出
        }
        APP_CONFIG = Config(default_config=default_settings)
        logger.info("配置已初始化。")
