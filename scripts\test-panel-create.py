#!/usr/bin/env python3
"""
测试 ezviz-panel-create 命令的脚本
"""

import subprocess
import sys
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def run_command(cmd_args, description):
    """运行命令并检查结果"""
    logger.info(f"🧪 测试: {description}")
    logger.info(f"📝 命令: {' '.join(cmd_args)}")
    
    try:
        result = subprocess.run(
            cmd_args,
            capture_output=True,
            text=True,
            timeout=30
        )
        
        if result.returncode == 0:
            logger.info("✅ 命令执行成功")
            return True
        else:
            logger.error(f"❌ 命令执行失败 (退出码: {result.returncode})")
            logger.error(f"错误输出: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        logger.error("❌ 命令执行超时")
        return False
    except Exception as e:
        logger.error(f"❌ 命令执行异常: {e}")
        return False


def main():
    """主测试函数"""
    logger.info("🚀 开始测试 ezviz-panel-create 命令")
    
    test_cases = [
        {
            "description": "显示帮助信息",
            "command": ["ledvance", "ezviz-panel-create", "--help"]
        },
        {
            "description": "标准面板创建 (dry-run)",
            "command": [
                "ledvance", "--dry-run", "ezviz-panel-create",
                "--name", "测试标准面板",
                "--rn-name", "test-standard-panel",
                "--category", "LightSource",
                "--sub-category", "SmartBulb"
            ]
        },
        {
            "description": "自定义面板创建 (dry-run)",
            "command": [
                "ledvance", "--dry-run", "ezviz-panel-create",
                "--type", "Custom",
                "--name", "测试自定义面板",
                "--rn-name", "test-custom-panel",
                "--pid", "TY_LDV_test123",
                "--category", "LightSource"
            ]
        },
        {
            "description": "完整参数面板创建 (dry-run)",
            "command": [
                "ledvance", "--dry-run", "ezviz-panel-create",
                "--name", "完整测试面板",
                "--rn-name", "full-test-panel",
                "--version", "V2.0.0",
                "--app-version", "2.0.16",
                "--desc", "这是一个完整的测试面板",
                "--category", "LightSource",
                "--sub-category", "SmartBulb",
                "--base", "base-panel"
            ]
        },
        {
            "description": "JSON 输出格式 (dry-run)",
            "command": [
                "ledvance", "--dry-run", "--output", "json", "ezviz-panel-create",
                "--name", "JSON测试面板",
                "--rn-name", "json-test-panel",
                "--category", "LightSource"
            ]
        },
        {
            "description": "生产环境面板创建 (dry-run)",
            "command": [
                "ledvance", "--dry-run", "ezviz-panel-create",
                "--env", "Prod",
                "--name", "生产环境面板",
                "--rn-name", "prod-test-panel",
                "--category", "LightSource"
            ]
        }
    ]
    
    # 错误测试用例
    error_test_cases = [
        {
            "description": "缺少必需参数 --name",
            "command": ["ledvance", "--dry-run", "ezviz-panel-create", "--rn-name", "test"],
            "should_fail": True
        },
        {
            "description": "缺少必需参数 --rn-name",
            "command": ["ledvance", "--dry-run", "ezviz-panel-create", "--name", "test"],
            "should_fail": True
        },
        {
            "description": "自定义面板缺少 --pid",
            "command": [
                "ledvance", "--dry-run", "ezviz-panel-create",
                "--type", "Custom",
                "--name", "test",
                "--rn-name", "test"
            ],
            "should_fail": True
        }
    ]
    
    success_count = 0
    total_tests = len(test_cases) + len(error_test_cases)
    
    # 运行正常测试用例
    for test_case in test_cases:
        if run_command(test_case["command"], test_case["description"]):
            success_count += 1
        print()  # 空行分隔
    
    # 运行错误测试用例
    logger.info("🔍 开始测试错误处理...")
    for test_case in error_test_cases:
        result = run_command(test_case["command"], test_case["description"])
        # 对于应该失败的测试，失败是成功的
        if test_case.get("should_fail", False):
            if not result:
                logger.info("✅ 错误处理正确")
                success_count += 1
            else:
                logger.error("❌ 应该失败但成功了")
        else:
            if result:
                success_count += 1
        print()  # 空行分隔
    
    # 输出测试结果
    logger.info("=" * 60)
    logger.info(f"📊 测试完成: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        logger.info("🎉 所有测试通过！")
        return 0
    else:
        logger.error(f"❌ {total_tests - success_count} 个测试失败")
        return 1


if __name__ == "__main__":
    sys.exit(main())
