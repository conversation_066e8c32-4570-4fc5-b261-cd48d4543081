@echo off
REM Windows批处理脚本 - Ledvance CLI生产环境安装

echo 🚀 Ledvance CLI 生产环境安装程序
echo ==========================================
echo.

REM 检查Python是否可用
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: 未找到Python
    echo 请先安装Python 3.8或更高版本
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python环境检查通过
python --version

REM 检查pip是否可用
python -m pip --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: pip不可用
    echo 请确保pip已正确安装
    pause
    exit /b 1
)

echo ✅ pip工具检查通过

REM 询问安装模式
echo.
echo 📦 选择安装模式:
echo 1. 生产模式 (推荐) - 正式使用
echo 2. 开发模式 - 开发调试
echo.
set /p choice="请选择 (1/2): "

if "%choice%"=="1" (
    set install_mode=production
    echo 选择的安装模式: 生产模式
) else if "%choice%"=="2" (
    set install_mode=development  
    echo 选择的安装模式: 开发模式
) else (
    echo ❌ 无效选择，使用默认的生产模式
    set install_mode=production
)

echo.
echo 📦 开始安装Ledvance CLI...

REM 执行安装
if "%install_mode%"=="production" (
    echo 正在安装 (生产模式)...
    python -m pip install .
) else (
    echo 正在安装 (开发模式)...
    python -m pip install -e .
)

if %errorlevel% neq 0 (
    echo ❌ 安装失败
    pause
    exit /b 1
)

echo ✅ Ledvance CLI安装成功

REM 验证安装
echo.
echo 🔍 验证安装...
ledvance --version
if %errorlevel% neq 0 (
    echo ❌ 安装验证失败
    pause
    exit /b 1
)

echo ✅ 安装验证通过

REM 询问是否安装自动补全
echo.
set /p completion="是否安装PowerShell自动补全? (y/N): "
if /i "%completion%"=="y" (
    echo 📦 安装自动补全...
    ledvance completion powershell --install
    if %errorlevel% equ 0 (
        echo ✅ 自动补全安装成功
    ) else (
        echo ⚠️  自动补全安装失败，可稍后手动配置
    )
) else (
    echo ⏭️  跳过自动补全安装
    echo 稍后可运行: ledvance completion powershell --install
)

REM 询问是否创建配置文件
echo.
if not exist "config.yaml" (
    set /p config="是否创建配置文件模板? (y/N): "
    if /i "%config%"=="y" (
        if exist "config.yaml.example" (
            copy "config.yaml.example" "config.yaml" >nul
            echo ✅ 配置文件已创建: config.yaml
            echo 💡 请编辑配置文件以适应您的环境
        ) else (
            echo ⚠️  未找到配置文件模板
        )
    ) else (
        echo ⏭️  跳过配置文件创建
    )
) else (
    echo ✅ 发现现有配置文件: config.yaml
)

REM 显示使用指南
echo.
echo ==========================================
echo 🎉 安装完成！
echo ==========================================
echo.
echo 📋 基本命令:
echo   ledvance --help                    # 查看帮助
echo   ledvance --version                 # 查看版本
echo   ledvance tuya-login                # 涂鸦平台登录
echo   ledvance ezviz-panel-check --help  # 查看面板检查帮助
echo.
echo 🔧 配置:
echo   编辑 config.yaml 文件配置环境参数
echo   设置环境变量覆盖配置
echo.
echo 📚 更多信息:
echo   docs\installation.md     # 详细安装指南
echo   docs\shell-completion.md # 自动补全指南
echo   README.md                # 项目说明
echo.

if "%install_mode%"=="development" (
    echo 💡 开发模式提示:
    echo   源码修改会立即生效
    echo   请不要删除源码目录
    echo.
)

echo 🚀 开始使用Ledvance CLI吧！
pause
