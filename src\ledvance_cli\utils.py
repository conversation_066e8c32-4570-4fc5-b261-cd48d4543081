"""
CLI 应用程序的实用工具函数。
"""
import functools
import hashlib
import json
import logging
import os
import random
import re
import time
import urllib.parse
from pathlib import Path
from typing import Any, Dict, List, Literal, Optional

import requests
import yaml
from rich.console import Console
from rich.table import Table

logger = logging.getLogger(__name__)
OutputType = Literal["text", "json", "yaml"]
console = Console()


def format_output(
    data: Any,
    output_format: OutputType = "text",
    title: str = "Output"
) -> None:
    """
    按指定格式格式化数据并打印到控制台。
    """
    if output_format == "json":
        console.print(json.dumps(data, indent=2, ensure_ascii=False))
    elif output_format == "yaml":
        console.print(yaml.dump(data, indent=2, allow_unicode=True))
    elif output_format == "text":
        if isinstance(data, dict):
            _print_dict_as_table(data, title)
        elif isinstance(data, list) and all(isinstance(i, dict) for i in data):
            _print_list_of_dicts_as_table(data, title)
        else:
            console.print(data)
    else:
        console.print(f"[bold red]错误: 未知输出格式 '{output_format}'[/bold red]")


def _print_dict_as_table(data: Dict[str, Any], title: str) -> None:
    """将字典打印为两列表格。"""
    table = Table(title=title, show_header=True, header_style="bold magenta")
    table.add_column("键", style="dim")
    table.add_column("值")
    for key, value in data.items():
        table.add_row(str(key), str(value))
    console.print(table)


def _print_list_of_dicts_as_table(data: List[Dict[str, Any]], title: str) -> None:
    """将字典列表打印为表格。"""
    if not data:
        console.print("无数据显示。")
        return
    headers = list(data[0].keys())
    table = Table(title=title, show_header=True, header_style="bold magenta")
    for header in headers:
        table.add_column(header.capitalize())
    for item in data:
        row = [str(item.get(header, "")) for header in headers]
        table.add_row(*row)
    console.print(table)


def retry(
    attempts: int = 3,
    initial_delay: float = 1.0,
    backoff_factor: float = 2.0,
    jitter: float = 1.0,
):
    """
    一个支持指数退避和随机抖动的重试装饰器。
    """
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            delay = initial_delay
            for attempt in range(1, attempts + 1):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    logger.warning(f"函数 {func.__name__} 第 {attempt}/{attempts} 次执行失败: {e}")
                    if attempt == attempts:
                        logger.error(f"函数 {func.__name__} 在 {attempts} 次尝试后最终失败。")
                        raise
                    backoff_delay = delay * (backoff_factor ** (attempt - 1))
                    total_delay = backoff_delay + random.uniform(0, jitter)
                    logger.info(f"将在 {total_delay:.2f} 秒后重试...")
                    time.sleep(total_delay)
        return wrapper
    return decorator


def show_progress(items, description="处理中..."):
    """
    为可迭代对象显示进度条。
    """
    from rich.progress import track
    return track(items, description=description)


def download_picture(url: str, temp_dir: str) -> Optional[str]:
    """从 URL 下载图片并保存到临时目录。"""
    if not os.path.exists(temp_dir):
        os.makedirs(temp_dir)
        logger.info(f"创建临时目录: {temp_dir}")
    try:
        response = requests.get(url, stream=True)
        response.raise_for_status()
        file_name = os.path.basename(urllib.parse.urlparse(url).path)
        file_path = os.path.join(temp_dir, file_name)
        with open(file_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                f.write(chunk)
        logger.info(f"图片已成功下载到: {file_path}")
        return file_path
    except requests.exceptions.RequestException as e:
        logger.error(f"下载图片失败: {e}")
        return None


def _process_sub_map(sub_map, main_category_id):
    """递归处理子品类映射的辅助函数。"""
    result = {}
    for sub_cat, items in sub_map.items():
        for item in items:
            main_category = item.get('mainCategory')
            category = item.get('category')
            if main_category and category:
                result[main_category] = main_category_id
                result[category] = item.get('id')
    return result


def convert_to_dict(data: List[Dict]) -> Dict:
    """将包含层级品类信息的列表转换为平面的 ID 映射字典。"""
    result = {}
    for item in data:
        main_category_id = item.get('id')
        main_category = item.get('mainCategory')
        if main_category and main_category_id:
            result[main_category] = main_category_id
        sub_map = item.get('subMap', {})
        if sub_map:
            sub_result = _process_sub_map(sub_map, main_category_id)
            result.update(sub_result)
    return result


def increment_version(version: str) -> str:
    """递增一个版本号字符串 (例如 'V1.0.0' -> 'V1.0.1')。"""
    if not version.startswith('V'):
        return version
    parts = version.lstrip('V').split('.')
    try:
        parts[-1] = str(int(parts[-1]) + 1)
        return 'V' + '.'.join(parts)
    except (ValueError, IndexError):
        logger.warning(f"无法递增无效的版本号格式: {version}")
        return version


def read_md5(file_path: str) -> Optional[str]:
    """从文件中读取第一行内容，通常用于读取 MD5 值。"""
    try:
        with open(file_path, 'r') as file:
            return file.readline().strip()
    except FileNotFoundError:
        logger.error(f"MD5 文件未找到: {file_path}")
        return None
    except Exception as e:
        logger.error(f"读取 MD5 文件时出错: {e}")
        return None


def snake_to_pascal(snake_str: str) -> str:
    """将蛇形命名 (snake_case) 转换为帕斯卡命名 (PascalCase)。"""
    return ''.join(x.capitalize() for x in snake_str.split('_'))


def get_tuya_cookies_path(cookies_file: str = "tuya-cookies.json"):
    """获取涂鸦 cookies 文件的路径。"""
    home = Path.home()
    # ~/.config/ledvance/ (XDG 标准)
    cookies_path = home / ".config" / "ledvance" / cookies_file
    os.makedirs(cookies_path.parent, exist_ok=True)
    return cookies_path
