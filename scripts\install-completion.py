#!/usr/bin/env python3
"""
Ledvance CLI 自动补全安装脚本

这个脚本会自动检测用户的shell环境并安装相应的自动补全配置。
"""

import os
import platform
import subprocess
import sys
from pathlib import Path


def detect_shell():
    """检测当前使用的shell"""
    shell_env = os.environ.get('SHELL', '')
    
    if 'bash' in shell_env:
        return 'bash'
    elif 'zsh' in shell_env:
        return 'zsh'
    elif 'fish' in shell_env:
        return 'fish'
    elif platform.system() == 'Windows':
        return 'powershell'
    else:
        return 'bash'  # 默认


def check_ledvance_installed():
    """检查ledvance CLI是否已安装"""
    try:
        result = subprocess.run(['ledvance', '--version'], 
                              capture_output=True, text=True, check=True)
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        return False


def install_completion_for_shell(shell):
    """为指定shell安装补全"""
    try:
        result = subprocess.run(['ledvance', 'completion', shell, '--install'], 
                              capture_output=True, text=True, check=True)
        print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"安装{shell}补全失败: {e.stderr}")
        return False
    except FileNotFoundError:
        print("错误: 找不到ledvance命令，请确保已正确安装")
        return False


def main():
    print("🚀 Ledvance CLI 自动补全安装程序")
    print("=" * 50)
    
    # 检查ledvance是否已安装
    if not check_ledvance_installed():
        print("❌ 错误: 未找到ledvance CLI工具")
        print("请先安装ledvance CLI:")
        print("  pip install -e .")
        sys.exit(1)
    
    print("✅ 检测到ledvance CLI已安装")
    
    # 检测shell
    detected_shell = detect_shell()
    print(f"🔍 检测到shell: {detected_shell}")
    
    # 询问用户确认
    response = input(f"是否为{detected_shell}安装自动补全? (y/N): ").strip().lower()
    
    if response in ['y', 'yes']:
        print(f"📦 正在为{detected_shell}安装自动补全...")
        
        if install_completion_for_shell(detected_shell):
            print("🎉 自动补全安装成功!")
            
            if detected_shell in ['bash', 'zsh']:
                print("\n💡 提示:")
                print("  请运行以下命令或重新启动终端以启用补全:")
                if detected_shell == 'bash':
                    print("    source ~/.bashrc")
                elif detected_shell == 'zsh':
                    print("    source ~/.zshrc")
            elif detected_shell == 'fish':
                print("\n💡 提示:")
                print("  Fish shell的补全会自动生效，无需额外操作")
            elif detected_shell == 'powershell':
                print("\n💡 提示:")
                print("  请重新启动PowerShell以启用补全功能")
                
            print("\n🔧 使用方法:")
            print("  ledvance <TAB>          # 显示所有可用命令")
            print("  ledvance tuya-<TAB>     # 显示tuya相关命令")
            print("  ledvance --<TAB>        # 显示全局选项")
            
        else:
            print("❌ 自动补全安装失败")
            sys.exit(1)
    else:
        print("取消安装")
        print("\n手动安装方法:")
        print(f"  ledvance completion {detected_shell} --install")


if __name__ == "__main__":
    main()
