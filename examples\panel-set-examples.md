# Ezviz Panel Set 命令使用示例

本文档展示了如何使用新的 `ezviz-panel-set` 命令来为多个产品批量设置面板。

## 原始脚本 vs 新命令对比

### 原始脚本调用方式
```bash
python script.py --env Test --name "test-panel" --pids TY_LDV_001 TY_LDV_002 TY_LDV_003
```

### 新命令调用方式
```bash
ledvance ezviz-panel-set --env Test --name "test-panel" --pids "TY_LDV_001,TY_LDV_002,TY_LDV_003"
```

## 参数映射表

| 原始脚本参数 | 新命令参数 | 说明 |
|-------------|-----------|------|
| `--env` | `--env` | 环境设置 (Prod/Test) |
| `--name` | `--name` | RN包名 |
| `--pids` (位置参数) | `--pids` | 产品ID列表，用逗号分隔 |

## 实际使用示例

### 1. 基本面板设置
```bash
# 为单个产品设置面板
ledvance ezviz-panel-set \
  --name "smart-bulb-panel" \
  --pids "TY_LDV_001"

# 为多个产品设置面板
ledvance ezviz-panel-set \
  --name "smart-bulb-panel" \
  --pids "TY_LDV_001,TY_LDV_002,TY_LDV_003"
```

### 2. 不同环境的面板设置
```bash
# 测试环境（默认）
ledvance ezviz-panel-set \
  --env Test \
  --name "test-panel" \
  --pids "TY_LDV_test001,TY_LDV_test002"

# 生产环境
ledvance ezviz-panel-set \
  --env Prod \
  --name "production-panel" \
  --pids "TY_LDV_prod001,TY_LDV_prod002,TY_LDV_prod003"
```

### 3. 大批量产品设置
```bash
# 为大量产品设置面板（会显示进度条）
ledvance ezviz-panel-set \
  --name "bulk-panel" \
  --pids "TY_LDV_001,TY_LDV_002,TY_LDV_003,TY_LDV_004,TY_LDV_005,TY_LDV_006,TY_LDV_007,TY_LDV_008,TY_LDV_009,TY_LDV_010"
```

## 预览模式（Dry-Run）

在实际设置面板之前，建议先使用 `--dry-run` 模式预览操作：

```bash
# 预览面板设置操作
ledvance --dry-run ezviz-panel-set \
  --name "test-panel" \
  --pids "TY_LDV_001,TY_LDV_002"

# 预览生产环境面板设置
ledvance --dry-run ezviz-panel-set \
  --env Prod \
  --name "prod-panel" \
  --pids "TY_LDV_prod001,TY_LDV_prod002"
```

## 输出格式

支持多种输出格式：

```bash
# JSON 格式输出
ledvance --output json ezviz-panel-set \
  --name "json-panel" \
  --pids "TY_LDV_001,TY_LDV_002"

# YAML 格式输出
ledvance --output yaml ezviz-panel-set \
  --name "yaml-panel" \
  --pids "TY_LDV_001,TY_LDV_002"
```

### JSON 输出示例
```json
[
  {
    "pid": "TY_LDV_001",
    "success": true,
    "panel_id": "panel_12345"
  },
  {
    "pid": "TY_LDV_002",
    "success": true,
    "panel_id": "panel_12345"
  }
]
```

### YAML 输出示例
```yaml
- panel_id: panel_12345
  pid: TY_LDV_001
  success: true
- panel_id: panel_12345
  pid: TY_LDV_002
  success: true
```

## 进度显示

命令会自动显示处理进度，特别是在处理大量产品时：

```bash
# 处理多个产品时会显示进度条
ledvance ezviz-panel-set --name "panel" --pids "TY_LDV_001,TY_LDV_002,TY_LDV_003"

# 输出示例：
# 正在设置产品面板... ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 100% 0:00:02
```

## 常见错误和解决方案

### 1. 缺少必需参数
```bash
# 错误：缺少 --name 参数
ledvance ezviz-panel-set --pids "TY_LDV_001"
# Error: Missing option '--name'.

# 错误：缺少 --pids 参数
ledvance ezviz-panel-set --name "test-panel"
# Error: Missing option '--pids'.

# 正确：提供所有必需参数
ledvance ezviz-panel-set --name "test-panel" --pids "TY_LDV_001"
```

### 2. 空的产品ID列表
```bash
# 错误：空的产品ID列表
ledvance ezviz-panel-set --name "test-panel" --pids ""
# Error: 产品ID列表不能为空

# 错误：只有逗号的列表
ledvance ezviz-panel-set --name "test-panel" --pids ",,,"
# Error: 产品ID列表不能为空

# 正确：提供有效的产品ID
ledvance ezviz-panel-set --name "test-panel" --pids "TY_LDV_001,TY_LDV_002"
```

### 3. 面板不存在
如果指定的面板名称不存在，命令会报错：
```bash
ledvance ezviz-panel-set --name "non-existent-panel" --pids "TY_LDV_001"
# Error: 面板 [non-existent-panel] 列表为空或未找到
```

### 4. 部分产品设置失败
如果某些产品设置失败，命令会继续处理其他产品，并在最后显示失败的产品列表：
```
📊 面板设置结果摘要:
✅ 成功: 2/3
❌ 失败: 1
失败的产品ID: TY_LDV_003
```

## 实际工作流程

### 1. 开发阶段
```bash
# 1. 先在测试环境预览
ledvance --dry-run ezviz-panel-set \
  --env Test \
  --name "new-panel" \
  --pids "TY_LDV_test001,TY_LDV_test002"

# 2. 确认无误后执行
ledvance ezviz-panel-set \
  --env Test \
  --name "new-panel" \
  --pids "TY_LDV_test001,TY_LDV_test002"
```

### 2. 生产部署
```bash
# 1. 先预览生产环境操作
ledvance --dry-run ezviz-panel-set \
  --env Prod \
  --name "production-panel" \
  --pids "TY_LDV_prod001,TY_LDV_prod002,TY_LDV_prod003"

# 2. 使用 JSON 输出便于后续处理
ledvance --output json ezviz-panel-set \
  --env Prod \
  --name "production-panel" \
  --pids "TY_LDV_prod001,TY_LDV_prod002,TY_LDV_prod003" > panel_set_results.json
```

## 新功能优势

相比原始脚本，新命令提供了以下优势：

1. **统一的 CLI 界面**：与其他 ledvance 命令保持一致
2. **进度显示**：使用 Rich 库显示美观的进度条
3. **Dry-Run 支持**：可以预览操作而不实际执行
4. **多种输出格式**：支持 text、json、yaml 格式
5. **更好的错误处理**：清晰的错误信息和部分失败处理
6. **批量处理**：高效处理大量产品
7. **详细日志**：完整的操作日志记录
8. **参数验证**：严格的参数验证和友好的错误提示

## 性能考虑

- 命令会按顺序处理每个产品，确保操作的可靠性
- 进度条提供实时反馈，让用户了解处理状态
- 即使某些产品设置失败，命令也会继续处理其他产品
- 支持大批量操作，适合生产环境使用

## 迁移建议

1. **逐步迁移**：先在测试环境使用新命令
2. **使用 Dry-Run**：在实际操作前先预览
3. **批量处理**：利用新命令的批量处理能力提高效率
4. **监控结果**：使用 JSON 输出格式便于自动化处理
5. **错误处理**：关注部分失败的情况，及时处理失败的产品
