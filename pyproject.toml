[build-system]
requires = ["setuptools>=61.0"]
build-backend = "setuptools.build_meta"

[project]
name = "ledvance_cli"
version = "0.1.0"
authors = [
  { name="<PERSON>", email="<EMAIL>" },
]
description = "一个现代化且可扩展的Tuya和Ezviz平台管理工具"
readme = "README.md"
requires-python = ">=3.8"
classifiers = [
    "Programming Language :: Python :: 3",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
]
dependencies = [
    "click",
    "python-dotenv",
    "PyYAML",
    "rich",
    "selenium",
    "requests",
]

[project.urls]
"Homepage" = "https://github.com/we6288815/ledvance-cli"
"Bug Tracker" = "https://github.com/we6288815/ledvance-cli/issues"

[project.scripts]
ledvance = "ledvance_cli.cli:main"
