"""
Ezviz 面板灰度命令 - 专门用于面板灰度操作。
"""
import logging
from typing import List, Optional

import click

from ledvance_cli import config as app_config
from ledvance_cli.core import ezviz_api
from ledvance_cli.core.exceptions import CLIError
from ledvance_cli.utils import format_output, show_progress

logger = logging.getLogger(__name__)


@click.command("ezviz-product-feature-update")
@click.option(
    "--env",
    default="Test",
    type=click.Choice(['Prod', 'Test'], case_sensitive=True),
    help="要操作的平台环境：'Prod' (生产) 或 'Test' (测试)。"
)
@click.option(
    "--features",
    required=True,
    type=str,
    help="需要更新的功能列表，用逗号分隔。"
)
@click.option(
    "--keyword",
    default="TY_LDV_",
    type=str,
    help="需要更新的产品关键字。"
)
@click.pass_context
def ezviz_panel_gray(
    ctx: click.Context,
    env: str,
    features: str,
    keyword: str
):
    """
    Ezviz 产品功能更新命令。

    此命令支持：
    1. 更新产品功能
    """
    is_dry_run = ctx.obj.dry_run

    if is_dry_run:
        logger.info("🔍 [DRY-RUN] Ezviz面板灰度操作预览")
        logger.info(f"🌐 目标环境: {env}")
        logger.info(f"🏷️ 产品feature列表: {features}")
        logger.info("💡 注意: 在dry-run模式下，不会进行实际的API调用")
        return

    logger.info(f"开始更新产品功能操作 (环境: {env}, features: {features}, keyword: {keyword})")

    try:
        # 解析feature列表
        feature_list = [feature.strip() for feature in features.split(',') if feature.strip()]
        base_payload = {
            'keyword': keyword
        }
        products = ezviz_api.get_all_product(base_payload, env)
        for product in show_progress(products, description="正在处理产品功能更新..."):
            product_id = product['productId']
            logger.info(f"开始处理产品: {product_id}")
            # 获取产品功能列表
            product_features = ezviz_api.get_product_features(product_id, env)
            if not product_features or not product_features['templateFeatures']:
                logger.warning(f"产品 {product_id} 没有选择标准功能。")
                continue
            template_features = product_features['templateFeatures']
            has_feature = any(f['deKey'] in feature_list for f in template_features)
            if not has_feature:
                logger.info(f"产品 {product_id} 不包含需要更新的功能。")
                continue

            if product['publishStatus'] == 2:
                logger.warning(f"产品 {product_id} 已发布，需要取消发布。")
                ezviz_api.unpublish_product(product_id, env)

            for feature in feature_list:
                _update_product_feature(template_features, feature, env, is_dry_run)
            
            _deal_with_publish(product_id, 'trial_produce_ing', env=env)
            _deal_with_publish(product_id, 'trial_produce_ed', env=env)

            logger.info(f"产品 {product_id} 更新成功。")

    except CLIError as e:
        logger.error(f"产品功能更新操作时出错: {e}")
        ctx.exit(1)
    except Exception as e:
        logger.error(f"发生意外错误: {e}", exc_info=True)
        ctx.exit(1)

    logger.info("产品功能更新操作完成。")

def _update_product_feature(template_features: list[dict], feature: str, env: str, is_dry_run: bool) -> bool:
    """更新产品功能。"""
    if is_dry_run:
        logger.info(f"空运行模式：模拟更新产品功能[{feature}]")
        return True
    try:
        t_feature = next((x for x in template_features if x['deKey'] == feature), None)
        if not t_feature:
            logger.info(f"产品功能 [{feature}] 不存在")
            return True
        t_feature['deInput']['maxLength'] = None
        t_feature['sourceFeatureType'] = t_feature['featureType']
        result = ezviz_api.update_product_feature(t_feature, env)
        logger.info(f"更新 [{feature}] 功能结果: [{result}]")
        return result
    except Exception as e:
        logger.error(f"更新产品功能时出错: {e}")
        return False


def _deal_with_publish(pid: str, status: str, env: str):
    """处理产品的发布状态变更。"""
    publish_param = {
        'pId': pid,
        'firmwareUpgradeCode': 'V1.0.0 build 201231',
        'publishStatus': status
    }
    res = ezviz_api.publish_product(publish_param, env=env)
    logger.info(f"将产品 {pid} 状态变更为 '{status}' 的结果: {res}")
