"""
CLI 应用程序的日志记录配置。
"""

import logging
from logging.handlers import RotatingFileHandler
import sys
from pathlib import Path
from typing import Literal

from rich.logging import RichHandler

LogLevel = Literal["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]

def setup_logging(log_level: LogLevel = "INFO", log_file: str = "app.log") -> None:
    """
    为应用程序设置日志记录。

    Args:
        log_level: 要显示的最低日志级别。
        log_file: 用于写入日志的文件。
    """
    log_format = logging.Formatter(
        "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )

    # 设置根日志记录器
    logger = logging.getLogger()
    logger.setLevel(log_level)
    logger.handlers = [] # 重置处理器

    # 使用 rich 格式的控制台处理器
    console_handler = RichHandler(
        rich_tracebacks=True,
        tracebacks_show_locals=True,
        show_path=False
    )
    console_handler.setLevel(log_level)
    logger.addHandler(console_handler)

    home = Path.home()
    # ~/.config/ledvance/ (XDG 标准)
    log_path = Path(home / ".config" / "ledvance" / log_file)
    # 带轮换功能的文件处理器
    try:
        log_path.parent.mkdir(parents=True, exist_ok=True)
        file_handler = RotatingFileHandler(
            log_path, maxBytes=1024 * 1024 * 5, backupCount=5  # 每个文件 5 MB
        )
        file_handler.setFormatter(log_format)
        file_handler.setLevel("DEBUG")  # 将所有级别的日志记录到文件
        logger.addHandler(file_handler)
    except (IOError, PermissionError) as e:
        logger.warning(f"无法设置文件日志记录到 {log_path}: {e}")

    # 将 stdout/stderr 重定向到日志系统
    # 这对于捕获外部库的输出很有用
    # sys.stdout = StreamToLogger(logger, logging.INFO)
    # sys.stderr = StreamToLogger(logger, logging.ERROR)

    logging.info(f"日志记录已初始化，级别为 {log_level}")

class StreamToLogger:
    """
    一个伪文件流对象，可将写入操作重定向到日志记录器实例。
    """
    def __init__(self, logger: logging.Logger, level: int):
        self.logger = logger
        self.level = level
        self.linebuf = ''

    def write(self, buf: str) -> None:
        for line in buf.rstrip().splitlines():
            self.logger.log(self.level, line.rstrip())

    def flush(self) -> None:
        pass
