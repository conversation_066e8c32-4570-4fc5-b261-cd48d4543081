# Ledvance CLI Shell 自动补全指南

Ledvance CLI 支持多种shell的Tab键自动补全功能，包括命令名称、选项参数和值的智能提示。

## 支持的Shell

- **Bash** (4.4+)
- **Zsh** 
- **Fish**
- **PowerShell** (Windows)

## 快速安装

### 自动安装（推荐）

运行自动安装脚本，它会检测您的shell环境并自动配置：

```bash
python scripts/install-completion.py
```

### 手动安装

#### 1. 使用内置命令

```bash
# 查看可用的shell选项
ledvance completion --help

# 为bash安装补全
ledvance completion bash --install

# 为zsh安装补全  
ledvance completion zsh --install

# 为fish安装补全
ledvance completion fish --install

# 为PowerShell安装补全
ledvance completion powershell --install
```

#### 2. 手动配置

如果自动安装失败，可以手动添加配置：

**Bash:**
```bash
# 生成补全脚本
ledvance completion bash > ~/.ledvance-completion.bash

# 添加到 ~/.bashrc
echo 'source ~/.ledvance-completion.bash' >> ~/.bashrc

# 或者直接添加eval命令
echo 'eval "$(_LEDVANCE_COMPLETE=bash_source ledvance)"' >> ~/.bashrc
```

**Zsh:**
```bash
# 添加到 ~/.zshrc
echo 'eval "$(_LEDVANCE_COMPLETE=zsh_source ledvance)"' >> ~/.zshrc
```

**Fish:**
```bash
# 创建补全文件
ledvance completion fish > ~/.config/fish/completions/ledvance.fish
```

**PowerShell:**
```powershell
# 生成补全脚本并添加到PowerShell配置文件
ledvance completion powershell >> $PROFILE
```

## 使用方法

安装完成后，重新启动终端或运行相应的source命令，然后就可以使用Tab键补全：

### 基本补全

```bash
# 补全命令名称
ledvance <TAB>
# 显示: ezviz-panel-check  ezviz-panel-upgrade  tuya-dp-check  tuya-login  tuya-sync-product

# 补全部分命令
ledvance tuya-<TAB>
# 显示: tuya-dp-check  tuya-login  tuya-sync-product

# 补全全局选项
ledvance --<TAB>
# 显示: --log-level  --output  --dry-run  --verbose  --version  --help
```

### 子命令补全

```bash
# 补全子命令选项
ledvance tuya-sync-product --<TAB>
# 显示: --env  --main-category  --category  --rn-name  --remark  --help

# 补全环境参数值
ledvance tuya-sync-product --env <TAB>
# 显示: Test  Prod
```

### 高级补全

某些参数支持智能值补全：

```bash
# 日志级别补全
ledvance --log-level <TAB>
# 显示: DEBUG  INFO  WARNING  ERROR  CRITICAL

# 输出格式补全
ledvance --output <TAB>
# 显示: text  json  yaml
```

## 补全功能特性

### 1. 命令发现
- 自动发现所有可用的CLI命令
- 支持动态加载的命令模块

### 2. 选项补全
- 全局选项（如 `--verbose`, `--dry-run`）
- 命令特定选项（如 `--env`, `--category`）

### 3. 值补全
- Choice类型参数的预定义值
- 文件路径补全（对于文件类型参数）
- 环境变量补全

### 4. 上下文感知
- 根据当前命令上下文提供相关补全
- 避免显示不相关的选项

## 故障排除

### 补全不工作

1. **检查安装**：
   ```bash
   # 验证ledvance是否正确安装
   ledvance --version
   
   # 检查补全脚本是否生成
   ledvance completion bash
   ```

2. **重新加载配置**：
   ```bash
   # Bash
   source ~/.bashrc
   
   # Zsh  
   source ~/.zshrc
   
   # Fish (自动生效)
   
   # PowerShell (重启终端)
   ```

3. **检查shell配置**：
   ```bash
   # 查看配置文件是否包含补全设置
   grep -i ledvance ~/.bashrc ~/.zshrc
   ```

### Windows特定问题

1. **PowerShell执行策略**：
   ```powershell
   # 如果遇到执行策略问题
   Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
   ```

2. **Git Bash**：
   ```bash
   # 在Git Bash中使用bash补全
   ledvance completion bash --install
   ```

### 权限问题

如果遇到权限问题：

```bash
# 确保配置文件可写
chmod +w ~/.bashrc ~/.zshrc

# 或者手动创建配置文件
touch ~/.bashrc
```

## 自定义补全

如果您需要为新命令添加自定义补全，可以：

1. **在命令定义中添加补全函数**：
   ```python
   def complete_env_vars(ctx, param, incomplete):
       return [k for k in os.environ if k.startswith(incomplete)]

   @click.option('--env-var', shell_complete=complete_env_vars)
   ```

2. **为Choice参数自动补全**：
   ```python
   @click.option('--env', type=click.Choice(['Test', 'Prod']))
   ```

## 更多信息

- [Click Shell Completion 官方文档](https://click.palletsprojects.com/en/stable/shell-completion/)
- [Bash Completion 指南](https://www.gnu.org/software/bash/manual/html_node/Programmable-Completion.html)
- [Zsh Completion 系统](http://zsh.sourceforge.net/Doc/Release/Completion-System.html)
