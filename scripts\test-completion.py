#!/usr/bin/env python3
"""
测试Ledvance CLI自动补全功能的脚本

这个脚本会验证各种shell的补全脚本是否能正确生成。
"""

import subprocess
import sys
import tempfile
from pathlib import Path


def test_completion_generation():
    """测试补全脚本生成"""
    shells = ['bash', 'zsh', 'fish', 'powershell']
    results = {}
    
    print("🧪 测试补全脚本生成...")
    print("=" * 50)
    
    for shell in shells:
        try:
            print(f"📝 测试 {shell} 补全脚本生成...")
            
            result = subprocess.run(
                ['ledvance', 'completion', shell],
                capture_output=True,
                text=True,
                check=True,
                timeout=30
            )
            
            if result.stdout.strip():
                results[shell] = True
                print(f"✅ {shell}: 成功生成补全脚本 ({len(result.stdout)} 字符)")
            else:
                results[shell] = False
                print(f"❌ {shell}: 补全脚本为空")
                
        except subprocess.CalledProcessError as e:
            results[shell] = False
            print(f"❌ {shell}: 生成失败 - {e.stderr}")
        except subprocess.TimeoutExpired:
            results[shell] = False
            print(f"❌ {shell}: 生成超时")
        except Exception as e:
            results[shell] = False
            print(f"❌ {shell}: 未知错误 - {e}")
    
    return results


def test_completion_content():
    """测试补全脚本内容"""
    print("\n🔍 测试补全脚本内容...")
    print("=" * 50)
    
    # 测试PowerShell补全脚本内容
    try:
        result = subprocess.run(
            ['ledvance', 'completion', 'powershell'],
            capture_output=True,
            text=True,
            check=True
        )
        
        content = result.stdout
        
        # 检查关键内容
        checks = [
            ('Register-ArgumentCompleter', '注册补全器'),
            ('ezviz-panel-check', '包含ezviz-panel-check命令'),
            ('tuya-sync-product', '包含tuya-sync-product命令'),
            ('--env', '包含--env选项'),
            ('--help', '包含--help选项'),
        ]
        
        for check, description in checks:
            if check in content:
                print(f"✅ {description}")
            else:
                print(f"❌ 缺少: {description}")
                
    except Exception as e:
        print(f"❌ 无法测试补全内容: {e}")


def test_help_command():
    """测试补全命令的帮助信息"""
    print("\n📖 测试补全命令帮助...")
    print("=" * 50)
    
    try:
        result = subprocess.run(
            ['ledvance', 'completion', '--help'],
            capture_output=True,
            text=True,
            check=True
        )
        
        help_content = result.stdout
        
        if 'bash|zsh|fish|powershell' in help_content:
            print("✅ 帮助信息包含所有支持的shell")
        else:
            print("❌ 帮助信息不完整")
            
        if '--install' in help_content:
            print("✅ 帮助信息包含--install选项")
        else:
            print("❌ 帮助信息缺少--install选项")
            
    except Exception as e:
        print(f"❌ 无法获取帮助信息: {e}")


def test_main_help():
    """测试主命令是否包含completion子命令"""
    print("\n🏠 测试主命令帮助...")
    print("=" * 50)
    
    try:
        result = subprocess.run(
            ['ledvance', '--help'],
            capture_output=True,
            text=True,
            check=True
        )
        
        help_content = result.stdout
        
        if 'completion' in help_content:
            print("✅ 主命令帮助包含completion子命令")
        else:
            print("❌ 主命令帮助缺少completion子命令")
            
    except Exception as e:
        print(f"❌ 无法获取主命令帮助: {e}")


def main():
    print("🚀 Ledvance CLI 自动补全功能测试")
    print("=" * 60)
    
    # 检查ledvance是否可用
    try:
        subprocess.run(['ledvance', '--version'], 
                      capture_output=True, check=True)
        print("✅ Ledvance CLI 可用")
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ Ledvance CLI 不可用，请先安装")
        sys.exit(1)
    
    # 运行测试
    results = test_completion_generation()
    test_completion_content()
    test_help_command()
    test_main_help()
    
    # 总结
    print("\n📊 测试总结")
    print("=" * 50)
    
    success_count = sum(1 for success in results.values() if success)
    total_count = len(results)
    
    print(f"补全脚本生成: {success_count}/{total_count} 成功")
    
    for shell, success in results.items():
        status = "✅" if success else "❌"
        print(f"  {status} {shell}")
    
    if success_count == total_count:
        print("\n🎉 所有测试通过！自动补全功能正常工作。")
        return 0
    else:
        print(f"\n⚠️  有 {total_count - success_count} 个测试失败。")
        return 1


if __name__ == "__main__":
    sys.exit(main())
