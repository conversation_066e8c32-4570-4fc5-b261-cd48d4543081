# Ledvance CLI Dry-Run 功能指南

## 概述

Ledvance CLI 的所有命令都支持 `--dry-run` 参数，该功能允许您预览命令将要执行的操作，而不实际执行任何可能产生副作用的操作。

## 全局 --dry-run 选项

`--dry-run` 是一个全局选项，可以与任何命令一起使用：

```bash
ledvance --dry-run <command> [options]
```

## 支持的命令

### 1. tuya-login - 涂鸦平台登录

```bash
# 预览登录操作
ledvance --dry-run tuya-login --output-file cookies.json
```

**Dry-run 行为：**
- 显示将要执行的登录步骤
- 不启动实际的浏览器会话
- 不保存任何cookies文件

### 2. tuya-dp-check - 涂鸦DP检查

```bash
# 预览DP检查操作
ledvance --dry-run tuya-dp-check --dp switch_led --category LightSource
```

**Dry-run 行为：**
- 显示查询参数和目标DP
- 不进行实际的API调用
- 不获取产品数据

### 3. tuya-sync-dp - 涂鸦DP同步

```bash
# 预览DP同步操作
ledvance --dry-run tuya-sync-dp --env test --category test_category --pid TY001
```

**Dry-run 行为：**
- 显示同步参数和目标信息
- 不获取涂鸦产品DP数据
- 不向Ezviz平台添加功能

### 4. tuya-sync-product - 涂鸦产品同步

```bash
# 预览产品同步操作
ledvance --dry-run tuya-sync-product \
  --env Test \
  --main-category main_cat \
  --category sub_cat \
  --rn-name panel_name \
  --pids TY001,TY002
```

**Dry-run 行为：**
- 显示同步参数和产品列表
- 不检查产品是否存在
- 不创建新产品
- 不上传图片或绑定面板

### 5. ezviz-panel-check - Ezviz面板检查

```bash
# 预览面板检查操作
ledvance --dry-run ezviz-panel-check --env prod --type gray
```

**Dry-run 行为：**
- 显示检查参数
- 使用模拟数据而非实际API调用
- 不保存检查结果文件

### 6. ezviz-panel-create - Ezviz面板创建

```bash
# 预览面板创建操作
ledvance --dry-run ezviz-panel-create \
  --name "测试面板" \
  --rn-name "test-panel" \
  --category "LightSource"
```

**Dry-run 行为：**
- 显示创建参数和配置信息
- 模拟检查 RN 名称重复
- 模拟文件上传过程
- 模拟面板创建和发布过程
- 不进行实际的 API 调用和文件操作

### 7. ezviz-panel-set - Ezviz面板设置

```bash
# 预览面板设置操作
ledvance --dry-run ezviz-panel-set \
  --name "test-panel" \
  --pids "TY_LDV_001,TY_LDV_002,TY_LDV_003"
```

**Dry-run 行为：**
- 显示设置参数和产品列表
- 模拟查找面板信息
- 模拟为每个产品设置面板
- 显示进度条和处理结果
- 不进行实际的 API 调用

### 8. ezviz-panel-upgrade - Ezviz面板升级

```bash
# 预览面板升级操作
ledvance --dry-run ezviz-panel-upgrade \
  --env test \
  --type Upgrade \
  --modules module1,module2 \
  --version 2.0.0
```

**Dry-run 行为：**
- 显示升级参数和模块列表
- 不进行实际的面板升级
- 不上传文件或修改面板状态
- 不设置灰度用户

## Dry-Run 输出格式

所有命令的dry-run输出都遵循统一的格式：

```
🔍 [DRY-RUN] <命令名称>操作预览
🎯 参数1: 值1
🌐 参数2: 值2
📂 参数3: 值3
🔄 将要执行的操作:
  1. 步骤1描述
  2. 步骤2描述
  3. 步骤3描述
💡 注意: 在dry-run模式下，不会进行实际的API调用/文件操作/数据修改
```

## 使用场景

### 1. 验证参数

在执行重要操作前，使用dry-run验证参数是否正确：

```bash
# 验证产品同步参数
ledvance --dry-run tuya-sync-product --env Prod --main-category 123 --category 456 --rn-name my_panel --pids TY001
```

### 2. 了解操作步骤

了解命令将要执行的具体步骤：

```bash
# 了解面板升级流程
ledvance --dry-run ezviz-panel-upgrade --env prod --type Upgrade --modules critical_module
```

### 3. 脚本调试

在自动化脚本中使用dry-run进行调试：

```bash
#!/bin/bash
# 先用dry-run验证
ledvance --dry-run tuya-sync-product --env Prod --main-category $MAIN_CAT --category $SUB_CAT --rn-name $PANEL --pids $PIDS

# 确认无误后执行实际操作
read -p "确认执行? (y/N): " confirm
if [[ $confirm == [yY] ]]; then
    ledvance tuya-sync-product --env Prod --main-category $MAIN_CAT --category $SUB_CAT --rn-name $PANEL --pids $PIDS
fi
```

## 测试 Dry-Run 功能

使用提供的测试脚本验证所有命令的dry-run功能：

```bash
# 运行dry-run功能测试
python scripts/test-dry-run.py
```

## 最佳实践

### 1. 生产环境操作前必须使用

在生产环境执行任何操作前，建议先使用dry-run：

```bash
# ❌ 直接在生产环境执行
ledvance ezviz-panel-upgrade --env prod --type Release --modules critical_module

# ✅ 先用dry-run验证
ledvance --dry-run ezviz-panel-upgrade --env prod --type Release --modules critical_module
# 确认无误后再执行实际操作
```

### 2. 批量操作前验证

批量操作前使用dry-run验证参数：

```bash
# 批量同步多个产品前先验证
ledvance --dry-run tuya-sync-product --env Prod --pids TY001,TY002,TY003,TY004
```

### 3. 结合日志级别使用

结合详细日志级别获取更多信息：

```bash
# 获取详细的dry-run信息
ledvance --log-level DEBUG --dry-run ezviz-panel-upgrade --env test --type Upgrade --modules test_module
```

## 注意事项

1. **Dry-run不会产生任何副作用**：不会创建、修改或删除任何数据
2. **某些命令可能使用模拟数据**：如ezviz-panel-check会使用模拟的面板版本数据
3. **网络请求不会发送**：所有API调用都会被跳过
4. **文件操作不会执行**：不会创建、修改或删除任何文件
5. **配置文件仍会被读取**：dry-run模式仍会读取配置文件以验证参数

## 故障排除

### Dry-run输出不显示

如果dry-run输出不显示，检查：

1. 确保使用了`--dry-run`参数
2. 检查日志级别设置
3. 验证命令语法是否正确

### 命令仍然执行实际操作

如果命令在dry-run模式下仍执行实际操作：

1. 检查命令实现是否正确处理了`ctx.obj.dry_run`
2. 报告bug给开发团队

## 开发者信息

如需为新命令添加dry-run支持，请参考现有命令的实现模式，确保：

1. 在函数开始处检查`ctx.obj.dry_run`
2. 提供详细的操作预览信息
3. 在dry-run模式下提前返回，不执行实际操作
4. 使用统一的输出格式
