@echo off
REM Windows批处理脚本 - 为Ledvance CLI设置PowerShell自动补全

echo 🚀 Ledvance CLI PowerShell自动补全设置
echo ==========================================

REM 检查ledvance是否已安装
ledvance --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: 未找到ledvance CLI工具
    echo 请先安装ledvance CLI:
    echo   pip install -e .
    pause
    exit /b 1
)

echo ✅ 检测到ledvance CLI已安装

REM 安装PowerShell补全
echo 📦 正在安装PowerShell自动补全...
ledvance completion powershell --install

if %errorlevel% equ 0 (
    echo.
    echo 🎉 PowerShell自动补全安装成功！
    echo.
    echo 💡 使用方法:
    echo   1. 重新启动PowerShell
    echo   2. 输入 'ledvance ' 然后按Tab键测试补全
    echo.
    echo 🔧 补全功能:
    echo   ledvance ^<TAB^>          # 显示所有可用命令
    echo   ledvance tuya-^<TAB^>     # 显示tuya相关命令  
    echo   ledvance --^<TAB^>        # 显示全局选项
    echo.
) else (
    echo ❌ 自动补全安装失败
    echo 请手动运行: ledvance completion powershell --install
)

pause
