# Ledvance CLI 安装指南

本指南提供了多种安装Ledvance CLI工具的方法，适用于不同的使用场景。

## 📦 安装方式概览

| 安装方式 | 适用场景 | 命令 |
|---------|---------|------|
| **生产安装** | 正式使用 | `pip install .` |
| **开发安装** | 开发调试 | `pip install -e .` |
| **从源码构建** | 自定义构建 | `python -m build` |
| **从wheel安装** | 离线安装 | `pip install ledvance_cli-*.whl` |

## 🚀 快速安装（推荐）

### 方法1: 直接从源码安装

```bash
# 1. 克隆或下载项目
git clone <repository-url>
cd ledvance-cli

# 2. 安装依赖和CLI工具
pip install .

# 3. 验证安装
ledvance --version
```

### 方法2: 使用安装脚本

```bash
# Windows
scripts/install-production.bat

# Linux/macOS  
scripts/install-production.sh
```

## 📋 详细安装步骤

### 1. 生产环境安装

**适用场景**: 正式使用，不需要修改源码

```bash
# 进入项目目录
cd ledvance-cli

# 安装CLI工具（生产模式）
pip install .

# 验证安装
ledvance --version
ledvance --help
```

**特点**:
- ✅ 安装到Python包目录
- ✅ 不依赖源码目录
- ✅ 可以删除源码目录
- ✅ 性能最佳

### 2. 开发环境安装

**适用场景**: 开发调试，需要修改源码

```bash
# 进入项目目录
cd ledvance-cli

# 安装CLI工具（开发模式）
pip install -e .

# 验证安装
ledvance --version
```

**特点**:
- ✅ 源码修改立即生效
- ✅ 便于调试和开发
- ❌ 依赖源码目录
- ❌ 不能删除源码目录

### 3. 从构建包安装

**适用场景**: 离线安装，分发给其他用户

```bash
# 1. 构建分发包
pip install build
python -m build

# 2. 安装构建的包
pip install dist/ledvance_cli-*.whl

# 或者安装tar.gz包
pip install dist/ledvance_cli-*.tar.gz
```

### 4. 虚拟环境安装

**适用场景**: 隔离环境，避免依赖冲突

```bash
# 1. 创建虚拟环境
python -m venv ledvance-env

# 2. 激活虚拟环境
# Windows
ledvance-env\Scripts\activate
# Linux/macOS
source ledvance-env/bin/activate

# 3. 安装CLI工具
pip install .

# 4. 验证安装
ledvance --version
```

## 🔧 系统要求

### Python版本
- **最低要求**: Python 3.8+
- **推荐版本**: Python 3.9+

### 依赖包
- click
- python-dotenv  
- PyYAML
- rich
- selenium
- requests

### 操作系统
- ✅ Windows 10/11
- ✅ macOS 10.15+
- ✅ Linux (Ubuntu 18.04+, CentOS 7+)

## 🛠️ 安装验证

安装完成后，运行以下命令验证：

```bash
# 检查版本
ledvance --version

# 查看帮助
ledvance --help

# 列出所有命令
ledvance --help | grep -A 20 "Commands:"

# 测试配置加载
ledvance tuya-login --help
```

## 🔄 更新和卸载

### 更新CLI工具

```bash
# 如果是从源码安装
cd ledvance-cli
git pull  # 更新源码
pip install . --upgrade

# 如果是从wheel安装
pip install ledvance_cli-*.whl --upgrade
```

### 卸载CLI工具

```bash
# 卸载包
pip uninstall ledvance-cli

# 清理配置文件（可选）
rm -rf ~/.config/ledvance/
rm -rf ~/.ledvance/
```

## 🚨 常见问题

### 1. 权限错误

**问题**: `Permission denied` 或需要管理员权限

**解决方案**:
```bash
# 使用用户安装
pip install . --user

# 或使用虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/macOS
# 或 venv\Scripts\activate  # Windows
pip install .
```

### 2. 依赖冲突

**问题**: 包版本冲突

**解决方案**:
```bash
# 使用虚拟环境隔离
python -m venv fresh-env
source fresh-env/bin/activate
pip install .
```

### 3. 命令未找到

**问题**: `ledvance: command not found`

**解决方案**:
```bash
# 检查安装路径
pip show ledvance-cli

# 检查PATH环境变量
echo $PATH

# 使用完整路径
python -m ledvance_cli.cli --help
```

### 4. 配置文件问题

**问题**: 配置文件加载失败

**解决方案**:
```bash
# 检查配置文件位置
ledvance --verbose --help

# 创建示例配置
cp config.yaml.example config.yaml
```

## 📦 分发和部署

### 创建分发包

```bash
# 安装构建工具
pip install build twine

# 构建分发包
python -m build

# 检查包内容
twine check dist/*
```

### 企业内部分发

```bash
# 1. 构建wheel包
python -m build --wheel

# 2. 复制到共享位置
cp dist/ledvance_cli-*.whl /shared/packages/

# 3. 用户安装
pip install /shared/packages/ledvance_cli-*.whl
```

### Docker部署

```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY . .

RUN pip install .

ENTRYPOINT ["ledvance"]
```

## 🔗 相关链接

- [Shell自动补全指南](shell-completion.md)
- [配置文件说明](../config.yaml.example)
- [开发指南](../README.md#开发)
- [故障排除](../README.md#故障排除)
