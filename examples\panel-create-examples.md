# Ezviz Panel Create 命令使用示例

本文档展示了如何使用新的 `ezviz-panel-create` 命令来替代原始的 Python 脚本。

## 原始脚本 vs 新命令对比

### 原始脚本调用方式
```bash
python script.py --env Prod --type Standard --name "测试面板" --rnName "test-panel" --version V1.0.0 --appVersion 1.0.0 --desc "测试描述" --category "LightSource" --subCategory "SmartBulb" --dir "D:/OneDrive - LEDVANCE GmbH/rn-package"
```

### 新命令调用方式
```bash
ledvance ezviz-panel-create --env Prod --type Standard --name "测试面板" --rn-name "test-panel" --version V1.0.0 --app-version 1.0.0 --desc "测试描述" --category "LightSource" --sub-category "SmartBulb" --dir "D:/OneDrive - LEDVANCE GmbH/rn-package"
```

## 参数映射表

| 原始脚本参数 | 新命令参数 | 说明 |
|-------------|-----------|------|
| `--env` | `--env` | 环境设置 (Prod/Test) |
| `--type` | `--type` | 面板类型 (Standard/Custom) |
| `--base` | `--base` | 基于面板名称 |
| `--name` | `--name` | 面板名称 |
| `--rnName` | `--rn-name` | RN包名 |
| `--version` | `--version` | RN版本 |
| `--appVersion` | `--app-version` | App版本 |
| `--desc` | `--desc` | 描述 |
| `--category` | `--category` | 一级品类 |
| `--subCategory` | `--sub-category` | 二级品类 |
| `--pid` | `--pid` | 产品ID |
| `--dir` | `--dir` | 包所在目录 |

## 实际使用示例

### 1. 创建标准面板
```bash
# 最简单的标准面板创建
ledvance ezviz-panel-create \
  --name "智能灯泡面板" \
  --rn-name "smart-bulb-panel" \
  --category "LightSource"

# 带完整参数的标准面板创建
ledvance ezviz-panel-create \
  --env Test \
  --type Standard \
  --name "高级智能灯泡面板" \
  --rn-name "advanced-smart-bulb-panel" \
  --version "V2.1.0" \
  --app-version "2.0.16" \
  --desc "支持调光调色的智能灯泡面板" \
  --category "LightSource" \
  --sub-category "SmartBulb"
```

### 2. 创建自定义面板
```bash
# 自定义面板（必须指定 PID）
ledvance ezviz-panel-create \
  --type Custom \
  --name "定制化灯泡面板" \
  --rn-name "custom-bulb-panel" \
  --pid "TY_LDV_w2oq3xw6qdrfpzrc" \
  --category "LightSource" \
  --sub-category "SmartBulb"
```

### 3. 生产环境面板创建
```bash
# 生产环境面板创建
ledvance ezviz-panel-create \
  --env Prod \
  --name "生产环境面板" \
  --rn-name "prod-panel-v1" \
  --version "V1.0.0" \
  --app-version "2.0.16" \
  --category "LightSource" \
  --desc "生产环境使用的面板"
```

### 4. 使用基础面板
```bash
# 基于现有面板创建新面板
ledvance ezviz-panel-create \
  --name "基于现有面板的新面板" \
  --rn-name "new-panel-based-on-existing" \
  --base "existing-panel-name" \
  --category "LightSource"
```

## 预览模式（Dry-Run）

在实际创建面板之前，建议先使用 `--dry-run` 模式预览操作：

```bash
# 预览面板创建操作
ledvance --dry-run ezviz-panel-create \
  --name "测试面板" \
  --rn-name "test-panel" \
  --category "LightSource"

# 预览自定义面板创建
ledvance --dry-run ezviz-panel-create \
  --type Custom \
  --name "自定义测试面板" \
  --rn-name "custom-test-panel" \
  --pid "TY_LDV_test123" \
  --category "LightSource"
```

## 输出格式

支持多种输出格式：

```bash
# JSON 格式输出
ledvance --output json ezviz-panel-create \
  --name "JSON输出面板" \
  --rn-name "json-output-panel" \
  --category "LightSource"

# YAML 格式输出
ledvance --output yaml ezviz-panel-create \
  --name "YAML输出面板" \
  --rn-name "yaml-output-panel" \
  --category "LightSource"
```

## 常见错误和解决方案

### 1. 缺少必需参数
```bash
# 错误：缺少 --name 参数
ledvance ezviz-panel-create --rn-name "test"
# Error: Missing option '--name'.

# 正确：提供所有必需参数
ledvance ezviz-panel-create --name "测试面板" --rn-name "test-panel"
```

### 2. 自定义面板缺少 PID
```bash
# 错误：自定义面板未指定 PID
ledvance ezviz-panel-create --type Custom --name "自定义面板" --rn-name "custom"
# Error: 自定义面板必须指定产品ID (--pid)

# 正确：为自定义面板指定 PID
ledvance ezviz-panel-create --type Custom --name "自定义面板" --rn-name "custom" --pid "TY_LDV_123"
```

### 3. 文件不存在
如果指定的目录中缺少必要的文件（海报图片、Android/iOS 包），命令会报错。确保以下文件存在：
- `poster/{base_name}.PNG`
- `cloud/{base_name}-android.zip`
- `cloud/{base_name}-ios.zip`
- `cloud/{base_name}-android.md5`
- `cloud/{base_name}-ios.md5`

## 新功能优势

相比原始脚本，新命令提供了以下优势：

1. **统一的 CLI 界面**：与其他 ledvance 命令保持一致
2. **Dry-Run 支持**：可以预览操作而不实际执行
3. **多种输出格式**：支持 text、json、yaml 格式
4. **更好的错误处理**：清晰的错误信息和退出码
5. **配置文件支持**：可以从配置文件读取默认值
6. **日志记录**：详细的操作日志
7. **参数验证**：更严格的参数验证
8. **帮助文档**：内置的帮助信息

## 迁移建议

1. **逐步迁移**：先在测试环境使用新命令
2. **使用 Dry-Run**：在实际操作前先预览
3. **检查配置**：确保配置文件中的设置正确
4. **验证文件**：确保所需的文件都存在
5. **测试输出**：验证输出格式符合预期
