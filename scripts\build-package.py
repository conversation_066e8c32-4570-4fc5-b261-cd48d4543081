#!/usr/bin/env python3
"""
Ledvance CLI 包构建脚本

这个脚本会构建可分发的wheel和tar.gz包，用于离线安装或分发给其他用户。
"""

import os
import shutil
import subprocess
import sys
import tempfile
from pathlib import Path


def print_header(title):
    """打印标题"""
    print(f"\n{'='*60}")
    print(f"📦 {title}")
    print('='*60)


def print_step(step, description):
    """打印步骤"""
    print(f"\n{step} {description}")
    print("-" * 40)


def check_build_requirements():
    """检查构建要求"""
    print_step("1️⃣", "检查构建环境")
    
    # 检查Python版本
    version = sys.version_info
    print(f"Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version < (3, 8):
        print("❌ 错误: 需要Python 3.8或更高版本")
        return False
    
    # 检查必要文件
    required_files = [
        'pyproject.toml',
        'src/ledvance_cli/__init__.py',
        'src/ledvance_cli/cli.py',
        'README.md'
    ]
    
    for file_path in required_files:
        if not Path(file_path).exists():
            print(f"❌ 缺少必要文件: {file_path}")
            return False
    
    print("✅ 构建环境检查通过")
    return True


def install_build_tools():
    """安装构建工具"""
    print_step("2️⃣", "安装构建工具")
    
    try:
        print("📦 安装build工具...")
        subprocess.run([sys.executable, '-m', 'pip', 'install', 'build', 'twine'], 
                      check=True)
        print("✅ 构建工具安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 构建工具安装失败: {e}")
        return False


def clean_build_artifacts():
    """清理构建产物"""
    print_step("3️⃣", "清理旧的构建产物")

    # 要清理的目录和文件
    cleanup_paths = [
        'dist',
        'build',
        'src/ledvance_cli.egg-info',
        '*.egg-info'
    ]

    def safe_remove(path):
        """安全删除文件或目录"""
        try:
            if os.path.isdir(path):
                # 在Windows上，先尝试修改权限
                if sys.platform == 'win32':
                    import stat
                    def handle_remove_readonly(func, path, exc):
                        os.chmod(path, stat.S_IWRITE)
                        func(path)
                    shutil.rmtree(path, onerror=handle_remove_readonly)
                else:
                    shutil.rmtree(path)
                print(f"🗑️  删除目录: {path}")
            else:
                os.remove(path)
                print(f"🗑️  删除文件: {path}")
            return True
        except Exception as e:
            print(f"⚠️  无法删除 {path}: {e}")
            return False

    for path_pattern in cleanup_paths:
        if '*' in path_pattern:
            # 处理通配符
            import glob
            for path in glob.glob(path_pattern):
                if os.path.exists(path):
                    safe_remove(path)
        else:
            path = Path(path_pattern)
            if path.exists():
                safe_remove(str(path))

    print("✅ 清理完成")
    return True


def build_packages():
    """构建包"""
    print_step("4️⃣", "构建分发包")
    
    try:
        print("🔨 构建wheel和tar.gz包...")
        result = subprocess.run([sys.executable, '-m', 'build'], 
                              capture_output=True, text=True, check=True)
        
        print("构建输出:")
        print(result.stdout)
        
        # 检查构建产物
        dist_dir = Path('dist')
        if not dist_dir.exists():
            print("❌ 构建失败: 未找到dist目录")
            return False
        
        built_files = list(dist_dir.glob('*'))
        if not built_files:
            print("❌ 构建失败: dist目录为空")
            return False
        
        print("✅ 构建成功，生成的文件:")
        for file_path in built_files:
            size = file_path.stat().st_size
            print(f"   📄 {file_path.name} ({size:,} bytes)")
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 构建失败: {e}")
        if e.stderr:
            print("错误输出:")
            print(e.stderr)
        return False


def verify_packages():
    """验证包"""
    print_step("5️⃣", "验证构建包")
    
    try:
        print("🔍 检查包完整性...")
        result = subprocess.run([sys.executable, '-m', 'twine', 'check', 'dist/*'], 
                              capture_output=True, text=True, check=True)
        
        print("验证输出:")
        print(result.stdout)
        print("✅ 包验证通过")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 包验证失败: {e}")
        if e.stderr:
            print("错误输出:")
            print(e.stderr)
        return False


def test_installation():
    """测试安装"""
    print_step("6️⃣", "测试包安装")
    
    # 创建临时虚拟环境进行测试
    with tempfile.TemporaryDirectory() as temp_dir:
        venv_path = Path(temp_dir) / 'test_env'
        
        try:
            print("🔧 创建测试虚拟环境...")
            subprocess.run([sys.executable, '-m', 'venv', str(venv_path)], 
                          check=True)
            
            # 确定虚拟环境中的Python路径
            if sys.platform == 'win32':
                venv_python = venv_path / 'Scripts' / 'python.exe'
                venv_pip = venv_path / 'Scripts' / 'pip.exe'
            else:
                venv_python = venv_path / 'bin' / 'python'
                venv_pip = venv_path / 'bin' / 'pip'
            
            # 找到wheel文件
            wheel_files = list(Path('dist').glob('*.whl'))
            if not wheel_files:
                print("❌ 未找到wheel文件")
                return False
            
            wheel_file = wheel_files[0]
            
            print(f"📦 在虚拟环境中安装 {wheel_file.name}...")
            subprocess.run([str(venv_pip), 'install', str(wheel_file)], 
                          check=True)
            
            print("🧪 测试安装的CLI...")
            result = subprocess.run([str(venv_python), '-m', 'ledvance_cli.cli', '--version'], 
                                  capture_output=True, text=True, check=True)
            
            print(f"✅ 测试成功: {result.stdout.strip()}")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ 测试失败: {e}")
            return False


def show_distribution_guide():
    """显示分发指南"""
    print_header("分发指南")
    
    dist_files = list(Path('dist').glob('*'))
    
    print("📦 构建的包文件:")
    for file_path in dist_files:
        print(f"   📄 {file_path}")
    
    print("\n🚀 安装方法:")
    
    # 找到wheel和tar.gz文件
    wheel_files = [f for f in dist_files if f.suffix == '.whl']
    tar_files = [f for f in dist_files if f.name.endswith('.tar.gz')]
    
    if wheel_files:
        wheel_file = wheel_files[0]
        print(f"\n1️⃣ 从wheel安装 (推荐):")
        print(f"   pip install {wheel_file}")
    
    if tar_files:
        tar_file = tar_files[0]
        print(f"\n2️⃣ 从源码包安装:")
        print(f"   pip install {tar_file}")
    
    print(f"\n3️⃣ 本地安装:")
    print(f"   pip install dist/ledvance_cli-*.whl")
    
    print(f"\n📤 分发方法:")
    print(f"   • 复制到共享目录供团队使用")
    print(f"   • 上传到私有PyPI服务器")
    print(f"   • 通过邮件或其他方式分享")
    
    print(f"\n🔧 用户安装:")
    print(f"   pip install /path/to/ledvance_cli-*.whl")


def main():
    """主函数"""
    print("📦 Ledvance CLI 包构建工具")
    print("=" * 60)
    print("这个脚本将构建可分发的Ledvance CLI包")
    
    # 执行构建步骤
    steps = [
        check_build_requirements,
        install_build_tools,
        clean_build_artifacts,
        build_packages,
        verify_packages,
        test_installation,
    ]
    
    for step in steps:
        if not step():
            print(f"\n❌ 构建失败，请检查错误信息")
            return 1
    
    # 显示分发指南
    show_distribution_guide()
    
    print(f"\n🎉 包构建成功!")
    print(f"📁 构建产物位于: dist/")
    return 0


if __name__ == "__main__":
    sys.exit(main())
